#include "dbserver.h"
#include <functional>
#include <QDataStream>
#include "log.h"
#include "engine/pddb.h"
#include "mechdb.h"
#include "alarmdb.h"
#include "common/commondefine.h"
#include "arresterdb.h"
#include "envdb.h"
#include "vibrationdb.h"
#include "webserver/commands.h"

#include "dbconfigutils.hpp"

#include "storageutils/storageutils.h"

#include "selfcheck/storageselfcheck.h"

#include "datafileclean.h"

#include "taskrecorddb.h"
#include <QtConcurrent>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonValue>
#include <QJsonParseError>

#define CONFIG_FILE_NAME  "/config.xml"



namespace storage {
class ExitGuard
{
    std::function<void()> m_exitPred;
public:
    explicit ExitGuard(std::function<void()> exitPred)
        : m_exitPred(exitPred)
    {}

    template <typename CreatePredicate,
              typename ExitPredicate>
    explicit ExitGuard(CreatePredicate createPred, ExitPredicate exitPred)
        :m_exitPred(exitPred)
    { createPred(); }

    ExitGuard(const ExitGuard&) = delete;
    ExitGuard& operator= (const ExitGuard&) = delete;


    ~ExitGuard()
    {
        if (m_exitPred)
        {
            m_exitPred();
        }
    }
};



namespace {
template <typename TargetSequnce,
          typename InputSequence,
          typename Predicate>
inline TargetSequnce transformSequence(InputSequence && input, Predicate pred)
{
    TargetSequnce rtn;
    std::transform(input.cbegin(), input.cend(), std::back_inserter(rtn), pred);
    return rtn;
}

inline ::TEVRecord convertToTevRecord(quint8 aduid, quint8 channelid, const PdDefine::TEVAmpDataInfo &record)
{
    ::TEVRecord rtn;
    rtn.recordTime = QDateTime::currentDateTime();
    rtn.aduId = QString::number(aduid);
    rtn.channelId = channelid;
    rtn.cMax = record.cMax;
    return rtn;
}

inline ::TEVRecord convertToTevRecord(const QString& aduid, quint8 channelid, float tevValue, const QDateTime& recordtime)
{
    ::TEVRecord rtn;
    rtn.recordTime = recordtime;
    rtn.aduId = aduid;
    rtn.channelId = channelid;
    rtn.cMax = tevValue;
    return rtn;
}

inline ::TEVRecord convertToTevRecord(quint8 aduid, quint8 channelid, float tevValue, const QDateTime& recordtime)
{
    return convertToTevRecord(QString::number(aduid), channelid, tevValue, recordtime);
}

template <typename T>
inline static auto unwrapQvariantFloatingData(const QVariant& input)
-> typename std::enable_if<std::is_floating_point<T>::value, T>::type
{
    return input.isNull()
            ? std::numeric_limits<T>::infinity()
            : qvariant_cast<T>(input);
}

constexpr auto kValueStr = "value";
constexpr auto kMaxStr = "max";
constexpr auto kAvgStr = "avg";
inline QByteArray serializeData(const ::TEMPRecord& input)
{
    QVariantMap variantMap{
        {kValueStr, input.temprature},
        {kMaxStr, input.max},
        {kAvgStr, input.avg},
    };
    QJsonDocument doc(QJsonObject::fromVariantMap(variantMap));
    return doc.toJson(QJsonDocument::Compact);
}

inline void retriveData(const QByteArray& input, ::TEMPRecord* data)
{
    auto variantMap = QJsonDocument::fromJson(input).object().toVariantMap();
    data->temprature = unwrapQvariantFloatingData<float>(variantMap[kValueStr]);
    data->max = unwrapQvariantFloatingData<float>(variantMap[kMaxStr]);
    data->avg = unwrapQvariantFloatingData<float>(variantMap[kAvgStr]);
}

inline QByteArray serializeData(const ::HumidityRecord& input)
{
    QVariantMap variantMap{
        {kValueStr, input.humanity},
        {kMaxStr, input.max},
        {kAvgStr, input.avg},
    };
    QJsonDocument doc(QJsonObject::fromVariantMap(variantMap));
    return doc.toJson(QJsonDocument::Compact);
}

inline void retriveData(const QByteArray& input, ::HumidityRecord* data)
{
    auto variantMap = QJsonDocument::fromJson(input).object().toVariantMap();
    data->humanity = unwrapQvariantFloatingData<float>(variantMap[kValueStr]);
    data->max = unwrapQvariantFloatingData<float>(variantMap[kMaxStr]);
    data->avg = unwrapQvariantFloatingData<float>(variantMap[kAvgStr]);
}


} // namespace


struct DBServer::Arg
{
    Arg(std::function<void(fCode::FCodeID, const QString&)> sender)
        : errorsender(sender)
    {}
    StorageSelfCheck errorsender;
};

/*************************************************
函数名： DBServer(QObject *parent = 0)
输入参数： parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
DBServer::DBServer()
    :m_transformFunctors{
{CHANNEL_TEMPERATURE, [this](const DataRecord& input){ return QVariant::fromValue(getTEMPRecord(input)); }},
{CHANNEL_HUMIDITY, [this](const DataRecord& input){ return QVariant::fromValue(getHumidityRecord(input)); }},
{CHANNEL_ARRESTER_I, [this](const DataRecord& input){ return QVariant::fromValue(getArresterRecord(input)); }},
{CHANNEL_GROUNDDINGCURRENT, [this](const DataRecord& input){ return QVariant::fromValue(getArresterRecord(input)); }},
{CHANNEL_LEAKAGECURRENT, [this](const DataRecord& input){ return QVariant::fromValue(getArresterRecord(input)); }},
{CHANNEL_NOISE, [this](const DataRecord& input){ return QVariant::fromValue(getEnvStatusRecord(input)); }},
{CHANNEL_VIBRATION, [this](const DataRecord& input){ return QVariant::fromValue(getVibrationRecord(input)); }},
}
{
    initDataTypeRegister();

    initSignalAndSlotConnect();

    init();
}
/*************************************************
函数名： init()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 初始化
*************************************************************/
void DBServer::init()
{
    m_serverThreadPool.setMaxThreadCount(5);

    m_pThread = new QThread(this);
    m_pThread->start();

    initDataBase();

//    m_iTimer = QObject::startTimer(60000);
    moveToThread(m_pThread);
    QObject::connect(&m_timer, &QTimer::timeout, [](){
        #ifdef Q_WS_QWS
                system("sync");
                system("echo 1 > /proc/sys/vm/drop_caches");
                system("free -m");
        #endif
    });
    m_timer.start(60000);

    //    m_pThread->start();
}

void DBServer::initDataTypeRegister()
{
    qRegisterMetaType<AduAlarmRecord>("AduAlarmRecord");
}

void DBServer::initSignalAndSlotConnect()
{
    connect(this, &DBServer::sigAddAduAlarmDataToDB, this, &DBServer::onAddAduAlarmRecordSlot);
}


/*************************************************
功能： 定时器事件处理
输入参数：
    event：定时器事件
*************************************************************/
//void DBServer::timerEvent(QTimerEvent *event)
//{
//    if (event->timerId() == m_iTimer)
//    {
//#ifdef Q_WS_QWS
//        system("sync");
//        system("echo 1 > /proc/sys/vm/drop_caches");
//        system("free -m");
//#endif
//    }
//}

/*************************************************
函数名： ~DBServer()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
DBServer::~DBServer()
{
    if (m_pThread)
    {
        m_pThread->exit();
        m_pThread->wait();
        delete m_pThread;
    }
    moveToThread(QThread::currentThread());

    QDir dir;
    for (int i = DB_MECH; i < DB_COUNT; i++)
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[i];
        pdb->saveRecordsToLocal();
        pdb->close();
        dir.remove( QDir::currentPath() + "/" + pdb->getMemDBName() );

        delete m_mapDBs[i];
    }

}

DBServer& DBServer::instance()
{
    return *instanceptr();
}

/*************************************************
函数名： clearAllData()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 清除全部数据
*************************************************************/
void DBServer::clearAllData()
{
//    for (int i = 0; i < DB_COUNT; i++)
//    {
//        SqliteDB* pdb = (SqliteDB*)m_mapDBs[i];
//        pdb->saveRecordsToLocal();
//        pdb->clearnData();
//    }
    auto keys = m_mapDBs.keys();
    for (auto& i : keys)
    {
        m_mapDBs[i]->saveRecordsToLocal();
        m_mapDBs[i]->clearnData();
    }
    m_storageengine->cleanDb();
    QDir dir(g_strDataFileDir);
    dir.setFilter(QDir::Dirs | QDir::Files);
    dir.setSorting(QDir::DirsFirst);
    QStringList filter;
    QFileInfoList fileList = dir.entryInfoList(filter);
    for (int i = 0; i < fileList.size(); i++)
    {
        dir.remove(fileList.at(i).absoluteFilePath());
    }

}

void DBServer::cleanUpHistoricalData(const int cleanCount)
{

    //清理局放类型数据库
    if(!m_storageengine.isNull())
    {
        m_storageengine->cleanUpHistoricalData(cleanCount);
    }

    //清理其他类型数据库
    QSet<DataBaseServiceType> dbSetExcludeClean = {DB_ALARM, DB_TASK_RECORD}; //不清理的数据库类型集合
    for(auto iter = m_mapDBs.cbegin(); iter != m_mapDBs.cend(); ++iter)
    {
        if( dbSetExcludeClean.contains(static_cast<DataBaseServiceType>(iter.key())) )
        {
            continue;
        }

        if( iter.value() )
        {
            iter.value()->cleanUpHistoricalData(cleanCount);
        }
    }

    //清理传感器告警数据
    if(m_mapDBs.contains(DB_ALARM))
    {
        AlarmDB* pAlarmDB = static_cast<AlarmDB*>(m_mapDBs[DB_ALARM]);
        if(pAlarmDB)
        {
            QStringList currentConfiguredAdus = m_configService->getAllADUID();
            pAlarmDB->cleanUpOldAduAlarmData(currentConfiguredAdus);
        }
    }
}

void DBServer::cleanUpHkCameraData(const QDateTime &beginTime, const QDateTime &endTime)
{
    SqliteDB* pdb = m_mapDBs[DB_HK_VIDEO];
    if(!pdb)
    {
        return;
    }

    pdb->cleanUpHistoricalData(beginTime, endTime);
}

/*************************************************
函数名： initDataBase()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 初始化数据库
*************************************************************/
void DBServer::initDataBase()
{
//    m_mapDBs[DB_AE] = new PDDB( g_stDataBases[DB_AE] );
//    m_mapDBs[DB_TEV] = new PDDB( g_stDataBases[DB_TEV] );
//    m_mapDBs[DB_UHFPRPS] = new PDDB( g_stDataBases[DB_UHFPRPS] );
//    m_mapDBs[DB_HFCTPRPS] = new PDDB( g_stDataBases[DB_HFCTPRPS] );
    m_mapDBs[DB_MECH] = new MechDB( g_stDataBases[DB_MECH] );
    m_mapDBs[DB_ALARM] = new AlarmDB( g_stDataBases[DB_ALARM] );
    m_mapDBs[DB_TEMP] = new ENVDB( g_stDataBases[DB_TEMP] );
    m_mapDBs[DB_HUMI] = new ENVDB( g_stDataBases[DB_HUMI] );
    m_mapDBs[DB_FROST_RAW] = new ENVDB( g_stDataBases[DB_FROST_RAW] );
    m_mapDBs[DB_FROST_ATM] = new ENVDB( g_stDataBases[DB_FROST_ATM] );
    m_mapDBs[DB_DEW_RAW] = new ENVDB( g_stDataBases[DB_DEW_RAW] );
    m_mapDBs[DB_DEW_ATM] = new ENVDB( g_stDataBases[DB_DEW_ATM] );
    m_mapDBs[DB_MOISTURE] = new ENVDB( g_stDataBases[DB_MOISTURE] );
    m_mapDBs[DB_PRESS_ABSO] = new ENVDB( g_stDataBases[DB_PRESS_ABSO] );
    m_mapDBs[DB_PRESS_NORM] = new ENVDB( g_stDataBases[DB_PRESS_NORM] );
    m_mapDBs[DB_DENSITY] = new ENVDB( g_stDataBases[DB_DENSITY] );
    m_mapDBs[DB_OXYGEN] = new ENVDB( g_stDataBases[DB_OXYGEN] );
    m_mapDBs[DB_SF6] = new ENVDB( g_stDataBases[DB_SF6] );
    m_mapDBs[DB_SF6_STATE] = new ENVDB( g_stDataBases[DB_SF6_STATE] );
    m_mapDBs[DB_ARRESTER] = new ArresterDB( g_stDataBases[DB_ARRESTER] );
    m_mapDBs[DB_VIBRATION] = new VibrationDB( g_stDataBases[DB_VIBRATION] );
    m_mapDBs[DB_CACC] = new PDDB( g_stDataBases[DB_CACC] );
    m_mapDBs[DB_LOW_TENSION_SWITCH] = new PDDB( g_stDataBases[DB_LOW_TENSION_SWITCH] );
    m_mapDBs[DB_METER] = new PDDB( g_stDataBases[DB_METER] );
    m_mapDBs[DB_LV_METER] = new PDDB( g_stDataBases[DB_LV_METER] );
    m_mapDBs[DB_ENV_STATUS] = new PDDB(g_stDataBases[DB_ENV_STATUS]);
    m_mapDBs[DB_WATER_LEVEL] = new PDDB( g_stDataBases[DB_WATER_LEVEL] );
    m_mapDBs[DB_FIBER_TEMP] = new PDDB( g_stDataBases[DB_FIBER_TEMP] );
    m_mapDBs[DB_HK_VIDEO] = new PDDB(g_stDataBases[DB_HK_VIDEO]);
    m_mapDBs[DB_DATA_WRAPPER] = new PDDB(g_stDataBases[DB_DATA_WRAPPER]);
    m_mapDBs[DB_FUKONG] = new PDDB( g_stDataBases[DB_FUKONG] );
    m_mapDBs[DB_TASK_RECORD] = new TaskRecordDB (g_stDataBases[DB_TASK_RECORD]);
    //m_mapDBs[DB_TEVPRPS] = new PDDB( g_stDataBases[DB_TEVPRPS] );

    for (int i = DB_MECH; i < DB_COUNT; i++)
    {
        m_mapDBs[i]->moveToThread(m_pThread);
        m_mapDBs[i]->open();
    }
}

/*************************************************
函数名： syncDB()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 将数据同步至本地
*************************************************************/
void DBServer::syncDB(void)
{
    for (int i = 0; i < DB_COUNT; i++)
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[i];
        pdb->saveRecordsToLocal();
    }
}

/*************************************************
函数名： instance()
输入参数： NULL
输出参数： NULL
返回值： 实例对象
功能： 获取实例
*************************************************************/
QSharedPointer<DBServer> DBServer::instanceptr()
{
    static QSharedPointer<DBServer> staticDBServer;
    if (!staticDBServer)
    {
        staticDBServer = QSharedPointer<DBServer>::create();
    }
    return staticDBServer;
}

/*************************************************
函数名： initInstance()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 初始化实例
*************************************************************/
QSharedPointer<DBServer> DBServer::initInstance(QSharedPointer<z058::config::ConfigServiceInterface> configService,
                                                QSharedPointer<z058::webserver::WebserverCommandInterface> webserverCommand,
                                                QSharedPointer<StorageServiceInterface> storageengine)
{
    {
        QReadLocker locker(&m_initLock);
        if (m_iniFlg)
        {
            return sharedFromThis();
        }
    }

    {
        QWriteLocker locker(&m_initLock);
        m_configService = configService;
        m_webserverCommand = webserverCommand;
        m_storageengine = storageengine;
        m_iniFlg = true;

        return sharedFromThis();
    }
}

void DBServer::setErrorCheck(std::function<void (fCode::FCodeID, const QString &)> sender)
{
    m_arg = QSharedPointer<Arg>(new Arg(sender));
}

bool DBServer::getLatestDataRecordKey(const QString &pointId, common::DataRecordKey &key)
{
    //AE、TEV、UHF、HFCT、tevprps数据查询使用storageengine
    Status status = m_storageengine->getLatestDataRecordKey(pointId, key);
    if (status.ok())
    {
        return true;
    }

    //获取测点对应的通道类型
    const QList<ADUChannelType> channelTypeList = m_configService->getChannelTypeListFromPoint(pointId);
    if (channelTypeList.isEmpty())
    {
        logError("get channel type list from point failed, pointId: ") << pointId;
        return false;
    }

    //排除ae,tev,uhf,hfct,tevprps
    const QSet<ADUChannelType> channelTypeSet{CHANNEL_AE, CHANNEL_TEV, CHANNEL_UHF, CHANNEL_HFCT, CHANNEL_TEVPRPS};
    if(channelTypeSet.contains(channelTypeList.first()))
    {
        return false;
    }

    //获取通道类型对应的数据库类型
    const DataBaseServiceType dbType = getDatabaseType(channelTypeList.first());
    if(DB_INVALID == dbType)
    {
        logError("get database type from channel type failed, channelType: ") << channelTypeList.first();
        return false;
    }

    if(!m_mapDBs.contains(dbType))
    {
        logError("m_mapDBs not contains dbType: ") << dbType;
        return false;
    }

    SqliteDB* pdb = static_cast<SqliteDB*>(m_mapDBs[dbType]);
    if(!pdb)
    {
        logError("pdb is null, dbType: ") << dbType;
        return false;
    }

    QList<ItemKeyWrapper> fields = {IK_ID, IK_RECORDTIME_T, SAMPLE_DATA_RECORD_ID};
    DataRecord record;
    pdb->getLastRecordFields(pointId, fields, record);
    if(!convertToDataRecordKey(record, key))
    {
        logError("convertToDataRecordKey failed, pointId: ") << pointId;
        return false;
    }
    
    return true;
}

bool DBServer::getLatestSensorStatus(const QString &pointId, common::SensorStatus &statusData)
{
    //AE、TEV、UHF、HFCT、tevprps数据查询使用storageengine
    Status status = m_storageengine->getLatestSensorStatus(pointId, statusData);
    if(status.ok())
    {
        return true;
    }

    //获取测点对应的通道类型
    const QList<ADUChannelType> channelTypeList = m_configService->getChannelTypeListFromPoint(pointId);
    if (channelTypeList.isEmpty())
    {
        logError("get channel type list from point failed, pointId: ") << pointId;
        return false;
    }

    //排除ae,tev,uhf,hfct,tevprps
    const QSet<ADUChannelType> channelTypeSet{CHANNEL_AE, CHANNEL_TEV, CHANNEL_UHF, CHANNEL_HFCT, CHANNEL_TEVPRPS};
    if(channelTypeSet.contains(channelTypeList.first()))
    {
        return false;
    }

    //获取通道类型对应的数据库类型
    const DataBaseServiceType dbType = getDatabaseType(channelTypeList.first());
    if(DB_INVALID == dbType)
    {
        logError("get database type from channel type failed, channelType: ") << channelTypeList.first();
        return false;
    }

    if(!m_mapDBs.contains(dbType))
    {
        logError("m_mapDBs not contains dbType: ") << dbType;
        return false;
    }

    SqliteDB* pdb = static_cast<SqliteDB*>(m_mapDBs[dbType]);
    if(!pdb)
    {
        logError("pdb is null, dbType: ") << dbType;
        return false;
    }
    QList<ItemKeyWrapper> fields = {SAMPLE_DATE_REMAIN_TIME, SAMPLE_SIGNAL_STRENGTH, SAMPLE_NOISE_RATIO, IK_RECORDTIME_T};
    DataRecord record;
    pdb->getLastRecordFields(pointId, fields, record);
    if(!convertToSensorStatus(record, statusData))
    {
        logError("convertToSensorStatus failed, pointId: ") << pointId;
        return false;
    }
    return true;
}

bool DBServer::getSensorStatusByTimeRange(const QString &pointId, const QDateTime &startTime, const QDateTime &endTime, QVector<common::SensorStatus> &statusData, int pageIndex, int pageSize)
{
    Status status = m_storageengine->getSensorStatusByTimeRange(pointId, startTime, endTime, pageSize, pageIndex, statusData);
    if(status.ok())
    {
        return true;
    }
    //获取测点对应的通道类型
    const QList<ADUChannelType> channelTypeList = m_configService->getChannelTypeListFromPoint(pointId);
    if (channelTypeList.isEmpty())
    {
        logError("get channel type list from point failed, pointId: ") << pointId;
        return false;
    }

    //排除ae,tev,uhf,hfct,tevprps
    const QSet<ADUChannelType> channelTypeSet{CHANNEL_AE, CHANNEL_TEV, CHANNEL_UHF, CHANNEL_HFCT, CHANNEL_TEVPRPS};
    if(channelTypeSet.contains(channelTypeList.first()))
    {
        return false;
    }

    //获取通道类型对应的数据库类型
    const DataBaseServiceType dbType = getDatabaseType(channelTypeList.first());
    if(DB_INVALID == dbType)
    {
        logError("get database type from channel type failed, channelType: ") << channelTypeList.first();
        return false;
    }

    if(!m_mapDBs.contains(dbType))
    {
        logError("m_mapDBs not contains dbType: ") << dbType;
        return false;
    }

    SqliteDB* pdb = static_cast<SqliteDB*>(m_mapDBs[dbType]);
    if(!pdb)
    {
        logError("pdb is null, dbType: ") << dbType;
        return false;
    }

    QList<ItemKeyWrapper> fields = {SAMPLE_DATE_REMAIN_TIME, SAMPLE_SIGNAL_STRENGTH, SAMPLE_NOISE_RATIO, IK_RECORDTIME_T};
    DataRecordList records;
    pdb->getRecordFieldsByTimeRange(pointId, fields, startTime, endTime, pageSize, pageIndex, records);
    for(const auto& record : records)
    {
        common::SensorStatus status;
        if(!convertToSensorStatus(record, status))
        {
            logError("convertToSensorStatus failed, pointId: ") << pointId;
            return false;
        }
        statusData.push_back(status);
    }
    return true;
}

bool DBServer::getDataRecordKeyByTimeRange(const QString &pointId, const QDateTime &startTime, const QDateTime &endTime, QVector<common::DataRecordKey> &keyList, int pageIndex, int pageSize)
{
    Status status = m_storageengine->getDataRecordKeyByTimeRange(pointId, startTime, endTime, pageSize, pageIndex, keyList);
    if(status.ok())
    {
        return true;
    }
    //获取测点对应的通道类型
    const QList<ADUChannelType> channelTypeList = m_configService->getChannelTypeListFromPoint(pointId);
    if (channelTypeList.isEmpty())
    {
        logError("get channel type list from point failed, pointId: ") << pointId;
        return false;
    }

    //排除ae,tev,uhf,hfct,tevprps
    const QSet<ADUChannelType> channelTypeSet{CHANNEL_AE, CHANNEL_TEV, CHANNEL_UHF, CHANNEL_HFCT, CHANNEL_TEVPRPS};
    if(channelTypeSet.contains(channelTypeList.first()))
    {
        return false;
    }

    //获取通道类型对应的数据库类型
    const DataBaseServiceType dbType = getDatabaseType(channelTypeList.first());
    if(DB_INVALID == dbType)
    {
        logError("get database type from channel type failed, channelType: ") << channelTypeList.first();
        return false;
    }

    if(!m_mapDBs.contains(dbType))
    {
        logError("m_mapDBs not contains dbType: ") << dbType;
        return false;
    }

    SqliteDB* pdb = static_cast<SqliteDB*>(m_mapDBs[dbType]);
    if(!pdb)
    {
        logError("pdb is null, dbType: ") << dbType;
        return false;
    }
    
    QList<ItemKeyWrapper> fields = {IK_ID, IK_RECORDTIME_T, SAMPLE_DATA_RECORD_ID};
    DataRecordList records;
    pdb->getRecordFieldsByTimeRange(pointId, fields, startTime, endTime, pageSize, pageIndex, records);
    for(const auto& record : records)
    {
        common::DataRecordKey key;
        if(!convertToDataRecordKey(record, key))
        {
            logError("convertToDataRecordKey failed, pointId: ") << pointId;
            return false;
        }
        keyList.push_back(key);
    }
    return true;
}

bool DBServer::getRecordCountByTimeRange(const QString& pointId, const QDateTime& startTime, const QDateTime& endTime, int& count)
{
    count = 0;

    Status status = m_storageengine->getRecordCountByTimeRange(pointId, startTime, endTime, count);
    if(status.ok())
    {
        return true;
    }
    //获取测点对应的通道类型
    const QList<ADUChannelType> channelTypeList = m_configService->getChannelTypeListFromPoint(pointId);
    if (channelTypeList.isEmpty())
    {
        logError("get channel type list from point failed, pointId: ") << pointId;
        return false;
    }

    //排除ae,tev,uhf,hfct,tevprps
    const QSet<ADUChannelType> channelTypeSet{CHANNEL_AE, CHANNEL_TEV, CHANNEL_UHF, CHANNEL_HFCT, CHANNEL_TEVPRPS};
    if(channelTypeSet.contains(channelTypeList.first()))
    {
        return false;
    }

    //获取通道类型对应的数据库类型
    const DataBaseServiceType dbType = getDatabaseType(channelTypeList.first());
    if(DB_INVALID == dbType)
    {
        logError("get database type from channel type failed, channelType: ") << channelTypeList.first();
        return false;
    }

    if(!m_mapDBs.contains(dbType))
    {
        logError("m_mapDBs not contains dbType: ") << dbType;
        return false;
    }

    SqliteDB* pdb = static_cast<SqliteDB*>(m_mapDBs[dbType]);
    if(!pdb)
    {
        logError("pdb is null, dbType: ") << dbType;
        return false;
    }
    return pdb->getRecordCountByTimeRange(pointId, startTime, endTime, count);
}

/*************************************************
功能： 添加一条局放数据
输入参数：
        record -- 数据记录结构体
        strChannelName -- 通道名
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::addAERecord( quint8 aduid, quint8 channelid, const PdDefine::AEAmpDataInfo& record, QVector<AERecord>* reportdata)
{
    PDS_SYS_INFO_LOG("addAERecord: aduid:  %d, channelid: %d", aduid, channelid);
    Monitor::AEAmpDataInfo tmprecord;
    tmprecord.datetime = QDateTime::currentDateTime();
    tmprecord.ucPwrFre = record.ucPwrFre;
    tmprecord.ucGain = record.ucGain;
    tmprecord.ucSyncType = record.ucSyncType;
    tmprecord.ucSyncFlag = record.ucSyncFlag;
    tmprecord.fFre1 = record.fFre1;
    tmprecord.fFre2 = record.fFre2;
    tmprecord.fMax = record.fMax;
    tmprecord.fRms = record.fRms;
    tmprecord.strADUID = QString::number(aduid);
    tmprecord.ucChannelID = channelid;

    auto rtn = addAERecord(DataTypeConvertionUtils::convertToAeRecord(tmprecord), reportdata);
    if (!rtn)
    {
        PDS_SYS_ERR_LOG("add ae record failed");
    }

    PDS_SYS_INFO_LOG("add addAERecord finished");

    return rtn;
}
bool DBServer::addTEVRecord(quint8 aduid, quint8 channelid, const PdDefine::TEVAmpDataInfo &record, QVector<TEVRecord>* reportdata)
{
    PDS_SYS_INFO_LOG("addTEVRecord: aduid:  %d, channelid: %d", aduid, channelid);
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    auto rtn = addTEVRecord(convertToTevRecord(aduid, channelid, record), reportdata);
    if (!rtn)
    {
        PDS_SYS_ERR_LOG("add tev record failed");
    }
    PDS_SYS_INFO_LOG("add addTEVRecord finished");
    return rtn;
}

bool DBServer::addTEVRecord( const TEVRecord &record, QVector<TEVRecord>* reportdata)
{
    PDS_SYS_INFO_LOG("addTEVRecord");
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    QString strChannelName = (record.channelName.isEmpty() ? getChannelName(record.aduId, record.channelId) : record.channelName);

    bool bAddRet = false;
    if (reportdata != nullptr)
    {
        reportdata->clear();
    }

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return bAddRet;
    }
    Status status = Status::OK();
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        common::DataWrapper tmpdata;
        common::PdTevData data;
        if (!(status = DataTypeConvertionUtils::convertToPdTevData(record,
                                                                   strChannelName,
                                                                   record.channelId,
                                                                   listPointArchiveInfo.at(i),
                                                                   &data)).ok())
        {
            if (m_arg)
            {
                m_arg->errorsender.datarecordError(QString("add data failed, pointId"));
            }
            PDS_SYS_ERR_LOG("convertToPdTevData:%s", status.ToString().c_str());
            return bAddRet;
        }
        tmpdata.setData(data);
        {
            //QMutexLocker locker(&m_engineMtx);
            if (!(status = m_storageengine->addRecord(data.pointInfo.pointId, tmpdata)).ok())
            {
                PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
                PDS_SYS_ERR_LOG("add addTEVRecord failed, pointId: %s", data.pointInfo.pointId.toLatin1().data());
                return bAddRet;
            }
            if (reportdata != nullptr)
            {
                reportdata->push_back(DataTypeConvertionUtils::convertFromPdTevData(data.pointInfo.pointId,                                                                                    data));
            }
        }
    }
    bAddRet = true;
    PDS_SYS_INFO_LOG("add addTEVRecord finished");
    return bAddRet;
}

bool DBServer::addTEVRecordSync(const TEVRecord &record, std::function<void(QVector<PDSMPDATA>&)> fDataReport)
{
    logInfo(QString("add Tev Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addTEVRecord currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    if(hasTEVRecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The tev data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }

    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    QString strChannelName = (record.channelName.isEmpty() ? getChannelName(record.aduId, record.channelId) : record.channelName);

    bool bAddRet = false;

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return bAddRet;
    }
    Status status = Status::OK();
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);

    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        common::DataWrapper tmpdata;
        common::PdTevData data;
        if (!(status = DataTypeConvertionUtils::convertToPdTevData(record,
                                                                  strChannelName,
                                                                  record.channelId,
                                                                  listPointArchiveInfo.at(i),
                                                                  &data)).ok())
        {          
            PDS_SYS_ERR_LOG("convertToPdTevData:%s", status.ToString().c_str());
            return bAddRet;
        }
        tmpdata.setData(data);
        {
            //QMutexLocker locker(&m_engineMtx);
            if (!(status = m_storageengine->addRecord(data.pointInfo.pointId, tmpdata)).ok())
            {               
                PDS_SYS_ERR_LOG("addTEVRecord failed: %s, pointId: %s", status.ToString().c_str(), data.pointInfo.pointId.toLatin1().data());
                return bAddRet;
            }

            if (fDataReport)
            {
                PDSMPDATA reportData;
                reportData.data.setValue(DataTypeConvertionUtils::convertFromPdTevData(data.pointInfo.pointId, data));
                reportDataVector.append(reportData);
            }
        }        
    }
    bAddRet = true;
    logInfo("add Tev data result: ") << bAddRet;

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("tev data report...");
        fDataReport(reportDataVector);
    }

    return bAddRet;
}

bool DBServer::addTEVRecordAsync(const TEVRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addTEVRecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addTEVRecordSync, record, fDataReport);

    return true;
}

bool DBServer::addUHFPRPSRecord(const PRPSRecord &record, std::function<void(QVector<PDSMPDATA>&)> fDataReport)
{
    logInfo(QString("add UHF Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addUHFPRPSRecord currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    logTrace("DBServer::hasUHFRecord start...");
    if(hasUHFRecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The uhf data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }
    logTrace("DBServer::hasUHFRecord end...");

    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    QString strChannelName = (record.channelName.isEmpty() ? getChannelName(record.aduId, record.channelId) : record.channelName);

    bool bAddRet = false;

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return bAddRet;
    }
    Status status = Status::OK();
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);

    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        common::DataWrapper tmpdata;
        common::PdUhfData data;
        if (!(status = DataTypeConvertionUtils::convertToPdUhfData(record,
                                                                   strChannelName,
                                                                   record.channelId,
                                                                   listPointArchiveInfo.at(i),
                                                                   &data)).ok())
        {
            PDS_SYS_ERR_LOG("convertToPdUhfData:%s", status.ToString().c_str());
            return bAddRet;
        }
        tmpdata.setData(data);
        {
            //QMutexLocker locker(&m_engineMtx);
            logTrace("DBServer::adduhfRecord start...");
            if (!(status = m_storageengine->addRecord(data.pointInfo.pointId, tmpdata)).ok())
            {
                PDS_SYS_ERR_LOG("addUHFRecord failed: %s, pointId: %s", status.ToString().c_str(), data.pointInfo.pointId.toLatin1().data());
                return bAddRet;
            }
            logTrace("DBServer::adduhfRecord end...");

            if (fDataReport)
            {
                PDSMPDATA reportData;
                reportData.data.setValue(DataTypeConvertionUtils::convertFromPdUhfData(data.pointInfo.pointId, data));
                reportDataVector.append(reportData);
            }
        }      
    }
    bAddRet = true;
    logInfo("add UHF data result: ") << bAddRet;

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("uhf data report...");
        fDataReport(reportDataVector);
    }

    return bAddRet;
}

bool DBServer::addUHFPRPSRecordAsync(const PRPSRecord &record, std::function<void(QVector<PDSMPDATA>&)> fDataReport)
{
    logTrace("addUHFPRPSRecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addUHFPRPSRecord, record, fDataReport);

    return true;
}

bool DBServer::addTEVPRPSRecordSync(const PRPSRecord &record, std::function<void(QVector<PDSMPDATA>&)> fDataReport)
{
    logInfo(QString("add TevPrps Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addTEVPRPSRecordSync currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    if(hasTEVPRPSRecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The TevPrps data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }

    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    QString strChannelName = (record.channelName.isEmpty() ? getChannelName(record.aduId, record.channelId) : record.channelName);

    bool bAddRet = false;

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return bAddRet;
    }
    Status status = Status::OK();
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);

    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        common::DataWrapper tmpdata;
        common::PdTEVPRPSData data;
        if (!(status = DataTypeConvertionUtils::convertToPdTEVPRPSData(record,
                                                                   strChannelName,
                                                                   record.channelId,
                                                                   listPointArchiveInfo.at(i),
                                                                   &data)).ok())
        {
            PDS_SYS_ERR_LOG("convertToPdTEVPRPSData:%s", status.ToString().c_str());
            return bAddRet;
        }
        tmpdata.setData(data);
        {
            //QMutexLocker locker(&m_engineMtx);
            if (!(status = m_storageengine->addRecord(data.pointInfo.pointId, tmpdata)).ok())
            {               
                PDS_SYS_ERR_LOG("addTEVPRPSRecord failed: %s, pointId: %s", status.ToString().c_str(), data.pointInfo.pointId.toLatin1().data());
                return bAddRet;
            }

            if (fDataReport)
            {
                PDSMPDATA reportData;
                reportData.data.setValue(DataTypeConvertionUtils::convertFromPdTEVPRPSData(data.pointInfo.pointId, data));
                reportDataVector.append(reportData);
            }
        }
    }
    bAddRet = true;
    logInfo("add TevPrps data result: ") << bAddRet;

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("TevPrps data report...");
        fDataReport(reportDataVector);
    }

    return bAddRet;
}

bool DBServer::addTEVPRPSRecordAsync(const PRPSRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addTEVPRPSRecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addTEVPRPSRecordSync, record, fDataReport);

    return true;
}

bool DBServer::addHFCTPRPSRecordAsync(const PRPSRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addHFCTPRPSRecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addHFCTPRPSRecord, record, fDataReport);

    return true;
}

bool DBServer::addHFCTPRPSRecord(const PRPSRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logInfo(QString("add HFCT Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addHFCTPRPSRecord currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    if(hasHFCTRecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The hfct data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }

    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    QString strChannelName = (record.channelName.isEmpty() ? getChannelName(record.aduId, record.channelId) : record.channelName);

    bool bAddRet = false;
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return bAddRet;
    }
    Status status = Status::OK();
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);

    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        common::DataWrapper tmpdata;
        common::PdHfctData data;
        if (!(status = DataTypeConvertionUtils::convertToPdHfctData(record,
                                                                    strChannelName,
                                                                    record.channelId,
                                                                    listPointArchiveInfo.at(i),
                                                                    &data)).ok())
        {
            PDS_SYS_ERR_LOG("convertToPdHfctData:%s", status.ToString().c_str());
             return bAddRet;
        }
        tmpdata.setData(data);
        {
            //QMutexLocker locker(&m_engineMtx);
            if (!(status = m_storageengine->addRecord(data.pointInfo.pointId, tmpdata)).ok())
            {             
                PDS_SYS_ERR_LOG("addHFCTRecord failed: %s, pointId: %s", status.ToString().c_str(), data.pointInfo.pointId.toLatin1().data());
                return bAddRet;
            }
            if (fDataReport)
            {
                PDSMPDATA reportData;
                reportData.data.setValue(DataTypeConvertionUtils::convertFromPdHfctData(data.pointInfo.pointId, data));
                reportDataVector.append(reportData);
            }
        }        
    }    
    bAddRet = true;
    logInfo("add HFCT data result: ") << bAddRet;

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("HFCT data report...");
        fDataReport(reportDataVector);
    }

    return bAddRet;
}

bool DBServer::addAERecord(const Monitor::AEAmpDataInfo &record, QVector<AERecord>* reportdata)
{
    return addAERecord(DataTypeConvertionUtils::convertToAeRecord(record), reportdata);
}

bool DBServer::addAERecord(const AERecord &record, QVector<AERecord> *reportdata)
{
    PDS_SYS_INFO_LOG("addAERecord");    
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    bool bAddRet = false;
    if (reportdata != nullptr)
    {
        reportdata->clear();
    }

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return bAddRet;
    }
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);
    Status status = Status::OK();
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        common::DataWrapper tmpdata;
        common::PdAeData aedata;
        if (!(status = DataTypeConvertionUtils::convertToPdAeData(record,
                                                                  getChannelName(record.aduId, record.channelId),
                                                                  record.channelId,
                                                                  listPointArchiveInfo.at(i),
                                                                  &aedata)).ok())
        {
            PDS_SYS_ERR_LOG("convertToPdAeData:%s", status.ToString().c_str());
            return bAddRet;
        }
        tmpdata.setData(aedata);
        {
            //QMutexLocker locker(&m_engineMtx);
            if (!(status = m_storageengine->addRecord(aedata.pointInfo.pointId, tmpdata)).ok())
            {
                if (m_arg)
                {
                    m_arg->errorsender.datarecordError(QString("add data failed, pointId: %1").arg(aedata.pointInfo.pointId));
                }
                PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
                PDS_SYS_ERR_LOG("add addAERecord failed, pointId: %s", aedata.pointInfo.pointId.toLatin1().data());               
                return bAddRet;
            }
            if (reportdata != nullptr)
            {
                /*if (!(status = m_storageengine->getLastRecord(aedata.pointInfo.pointId, &tmpdata)).ok())
                    {
                        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
                        PDS_SYS_ERR_LOG("add addAERecord failed, pointId: %s", aedata.pointInfo.pointId.toLatin1().data());
                        return false;
                    }
                    auto tmpunwrapdata = tmpdata.getData<common::PdAeData>();
                    if (!tmpunwrapdata)
                    {
                        PDS_SYS_ERR_LOG("add addAERecord failed, pointId: %s", aedata.pointInfo.pointId.toLatin1().data());
                        return false;
                    }
                    reportdata->push_back(DataTypeConvertionUtils::convertFromPdAeData(
                                              tmpunwrapdata->pointInfo.pointId, *tmpunwrapdata));
                    */
                reportdata->push_back(DataTypeConvertionUtils::convertFromPdAeData(aedata.pointInfo.pointId,aedata));
            }
        }       
    }
    bAddRet = true;

    PDS_SYS_INFO_LOG("add addAERecord finished");

    return bAddRet;
}

bool DBServer::addAERecordSync(const AERecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
//    const AERecord stAeData = DataTypeConvertionUtils::convertToAeRecord(record);

    logInfo(QString("add AE Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addAERecord currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    if(hasAERecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The AE data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }

    bool bAddRet = false;

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return bAddRet;
    }
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);
    Status status = Status::OK();

    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        common::DataWrapper tmpdata;
        common::PdAeData aedata;
        if (!(status = DataTypeConvertionUtils::convertToPdAeData(record,
                                                                  getChannelName(record.aduId, record.channelId),
                                                                  record.channelId,
                                                                  listPointArchiveInfo.at(i),
                                                                  &aedata)).ok())
        {
            PDS_SYS_ERR_LOG("convertToPdAeData:%s", status.ToString().c_str());
            return bAddRet;
        }
        tmpdata.setData(aedata);
        {
            //QMutexLocker locker(&m_engineMtx);
            if (!(status = m_storageengine->addRecord(aedata.pointInfo.pointId, tmpdata)).ok())
            {
                PDS_SYS_ERR_LOG("addAERecord failed:%s, pointId: %s", status.ToString().c_str(), aedata.pointInfo.pointId.toLatin1().data());
                return bAddRet;
            }

            if (fDataReport)
            {
                PDSMPDATA reportData;
                reportData.data.setValue(DataTypeConvertionUtils::convertFromPdAeData(aedata.pointInfo.pointId, aedata));
                reportDataVector.append(reportData);
            }
        }
    }
    bAddRet = true;
    logInfo("add Ae data result: ") << bAddRet;

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("Ae data report...");
        fDataReport(reportDataVector);
    }

    return bAddRet;
}

bool DBServer::addAERecordAsync(const AERecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addAERecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addAERecordSync, record, fDataReport);

    return true;
}

bool DBServer::addENVRecord(const Monitor::ENVDate &record)
{
    PDS_SYS_INFO_LOG("addENVRecord");
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    DataRecord dbRecord;

    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.usRecordID );
    dbRecord.setData( IK_RECORDTIME, record.datetime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, record.datetime.toTime_t() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, record.ucBattery );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(record.strADUID, record.ucChannelID) );//建表字段
    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_TEMP];
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.strADUID, record.ucChannelID);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
//        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(AE_GLOBAL_ID_START)) );
        if (!pdb->addRecord(dbRecord))
        {
            if (m_arg)
            {
                m_arg->errorsender.datarecordError(QString("add data failed, pointId: %1").arg(stPointArchiveInfo.strPointGUID));
            }
            PDS_SYS_ERR_LOG("add addENVRecord failed, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());
        }
    }

    PDS_SYS_INFO_LOG("add addAERecord finished, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());

    return true;
}

bool DBServer::addVibrationRecord(const VibrationRecord &record)
{
    PDS_SYS_INFO_LOG("addVibrationRecord");
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    DataRecord dbRecord;

    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.usRecordID );
    dbRecord.setData( IK_RECORDTIME, record.datetime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, record.datetime.toTime_t() );
    dbRecord.setData( VIBRATION_FREQUENCY, record.ucFrequency );
    dbRecord.setData( VIBRATION_SAMPLE_COUNT, record.ucSampleCycleCount);
    dbRecord.setData( VIBRATION_SAMPLE_RATE, record.usSampleCountCycle );
    dbRecord.setData( SAMPLE_DATA_BATTERY, record.uiBattery );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, record.strRecordID );
    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );

    QByteArray array;
    array.resize(record.stVibrationDataX.vecArray.size() * sizeof(double));
    memcpy( array.data(), &record.stVibrationDataX.vecArray[0],  array.size() );
    dbRecord.setData( VIBRATION_X_ARRAY, array );
    array.clear();

    array.resize(record.stVibrationDataY.vecArray.size() * sizeof(double));
    memcpy( array.data(), &record.stVibrationDataY.vecArray[0],  array.size() );
    dbRecord.setData( VIBRATION_Y_ARRAY, array );
    array.clear();

    array.resize(record.stVibrationDataZ.vecArray.size() * sizeof(double));
    memcpy( array.data(), &record.stVibrationDataZ.vecArray[0],  array.size() );
    dbRecord.setData( VIBRATION_Z_ARRAY, array );
    array.clear();

    dbRecord.setData( VIBRATION_X_AMP_MAX, record.stVibrationDataX.iAmplitudeMax );
    dbRecord.setData( VIBRATION_X_AMP_AVG, record.stVibrationDataX.iAmplitudeAverage );
    dbRecord.setData( VIBRATION_X_ACC_MAX, record.stVibrationDataX.dAccelerationMax );
    dbRecord.setData( VIBRATION_X_ACC_AVG, record.stVibrationDataX.dAccelerationAverage );

    array.resize(record.stVibrationDataX.vecMaxFreqs.size() * sizeof(double));
    memcpy( array.data(), &record.stVibrationDataX.vecMaxFreqs[0],  array.size() );
    dbRecord.setData( VIBRATION_X_MAX_FREQ, array );
    array.clear();

    dbRecord.setData( VIBRATION_Y_AMP_MAX, record.stVibrationDataY.iAmplitudeMax );
    dbRecord.setData( VIBRATION_Y_AMP_AVG, record.stVibrationDataY.iAmplitudeAverage );
    dbRecord.setData( VIBRATION_Y_ACC_MAX, record.stVibrationDataY.dAccelerationMax );
    dbRecord.setData( VIBRATION_Y_ACC_AVG, record.stVibrationDataY.dAccelerationAverage );

    array.resize(record.stVibrationDataY.vecMaxFreqs.size() * sizeof(double));
    memcpy( array.data(), &record.stVibrationDataY.vecMaxFreqs[0],  array.size() );
    dbRecord.setData( VIBRATION_Y_MAX_FREQ, array );
    array.clear();

    dbRecord.setData( VIBRATION_Z_AMP_MAX, record.stVibrationDataZ.iAmplitudeMax );
    dbRecord.setData( VIBRATION_Z_AMP_AVG, record.stVibrationDataZ.iAmplitudeAverage );
    dbRecord.setData( VIBRATION_Z_ACC_MAX, record.stVibrationDataZ.dAccelerationMax );
    dbRecord.setData( VIBRATION_Z_ACC_AVG, record.stVibrationDataZ.dAccelerationAverage );

    array.resize(record.stVibrationDataZ.vecMaxFreqs.size() * sizeof(double));
    memcpy( array.data(), &record.stVibrationDataZ.vecMaxFreqs[0],  array.size() );
    dbRecord.setData( VIBRATION_Z_MAX_FREQ, array );
    array.clear();

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(record.strADUID, record.ucChannelID) );//建表字段

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_VIBRATION];
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.strADUID, record.ucChannelID);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.strRecordID + QString::number(i) );

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
//        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(VIBRATION_GLOBAL_ID_START)) );
        int64_t currentId = -1;
        if (!pdb->addRecord(dbRecord, &currentId, nullptr))
        {
            PDS_SYS_ERR_LOG("add addVibrationRecord failed, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());
        }
    }

    //PDS_SYS_INFO_LOG("add addVibrationRecord failed, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());

    return true;
}

// 说明：一个避雷器测点，对应6个前端（ABC相电流、电压）
bool DBServer::addArresterRecord(const ArresterRecord &record, std::function<void(QVector<PDSMPDATA> &)> fDataReport)
{
    logInfo("add Arrester Record.");

    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    DataRecord dbRecord;

    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.usRecordID );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, record.strRecordID );
    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( IK_RECORDTIME, record.datetime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, record.datetime.toTime_t() );
    dbRecord.setData( ARRESTER_FREQUENCY, record.ucFrequency );
    dbRecord.setData( ARRESTER_HARMONIC_COUNT, record.ucHarmonicCount );
    dbRecord.setData( SAMPLE_DATA_BATTERY, record.uiBattery );
    dbRecord.setData( ARRESTER_CHANNEL_PHASE, record.eChannelPhase );
    dbRecord.setData( ARRESTER_DATA_RESISTIVE_CURRENT, record.fResistiveCurrent );
    dbRecord.setData( ARRESTER_DATA_LEAKAGE_CURRENT_CURRENT, record.fLeakageCurrent );

    QByteArray array;
    if (!record.vecCurrentDataChannel.isEmpty())
    {
        array.resize(record.vecCurrentDataChannel.size() * sizeof(float));
        memcpy( array.data(), &record.vecCurrentDataChannel[0],  array.size() );
    }
    dbRecord.setData( ARRESTER_DATA_I_CHANNEL, array );
    array.clear();

    if (!record.vecVoltageDataChannel.isEmpty())
    {
        array.resize(record.vecVoltageDataChannel.size() * sizeof(float));
        memcpy( array.data(), &record.vecVoltageDataChannel[0],  array.size() );
    }
    dbRecord.setData( ARRESTER_DATA_U_CHANNEL, array );
    array.clear();


    if (!record.vecResistiveCurrentData.isEmpty())
    {
        array.resize(record.vecResistiveCurrentData.size() * sizeof(float));
        memcpy( array.data(), &record.vecResistiveCurrentData[0],  array.size() );
    }
    dbRecord.setData( ARRESTER_RESISTIVE_CURRENT_CHANNEL, array );
    array.clear();

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(record.strADUID, record.ucChannelID) );//建表字段

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_ARRESTER];

    dbRecord.setData( SAMPLE_DATA_STATION_PMS, record.stPointArchiveInfo.strStationPMS );
    dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, record.stPointArchiveInfo.strDevicePMS );
    dbRecord.setData( SAMPLE_DATA_STATION_ID, record.stPointArchiveInfo.strStationGUID );
    dbRecord.setData( SAMPLE_DATA_DEVICE_ID, record.stPointArchiveInfo.strDeviceGUID );
    dbRecord.setData( SAMPLE_DATA_POINT_GUID, record.stPointArchiveInfo.strPointGUID );
//        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

    dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(ARRESTER_GLOBAL_ID_START)) );

    if(!pdb->addRecord( dbRecord ))
    {
        logError("add Arrester data failed, pointId: ") << record.stPointArchiveInfo.strPointGUID;
        return false;
    }

    logInfo("add Arrester data true.");

    if (fDataReport)
    {
        QVector<PDSMPDATA> reportDataVector; //上送数据

        PDSMPDATA reportData;
        reportData.data.setValue(record);
        reportDataVector.append(reportData);

        logInfo("Arrester data report...");
        fDataReport(reportDataVector);
    }

    return true;
}

bool DBServer::addArresterRecordAsync(const ArresterRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addArresterRecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addArresterRecord, record, fDataReport);

    return true;
}

bool DBServer::addArresterRecord(const QString &strADUID, quint8 ucChannelID, const QDateTime &recordTime, quint32 uiRecordID)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    DataRecord dbRecord;

    dbRecord.setData( IK_RECORDTIME, recordTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, recordTime.toTime_t() );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, uiRecordID );
    QString strChannelName = getChannelName(strADUID, ucChannelID );
    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, strChannelName );

    dbRecord.setData( SAMPLE_DATA_POINT_GUID, strChannelName );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_ARRESTER];

    return pdb->addRecord( dbRecord );
}
bool DBServer::updateArresterRecord(const ArresterRecord &record)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    DataRecord dbRecord;

    dbRecord.setData( ARRESTER_DATA_RESISTIVE_CURRENT, record.fResistiveCurrent );
    dbRecord.setData( ARRESTER_DATA_LEAKAGE_CURRENT_CURRENT, record.fLeakageCurrent );

    QByteArray array;
    if (!record.vecCurrentDataChannel.isEmpty())
    {
        array.resize(record.vecCurrentDataChannel.size() * sizeof(float));
        memcpy( array.data(), &record.vecCurrentDataChannel[0],  array.size() );
    }
    dbRecord.setData( ARRESTER_DATA_I_CHANNEL, array );
    array.clear();

    if (!record.vecVoltageDataChannel.isEmpty())
    {
        array.resize(record.vecVoltageDataChannel.size() * sizeof(float));
        memcpy( array.data(), &record.vecVoltageDataChannel[0],  array.size() );
    }
    dbRecord.setData( ARRESTER_DATA_U_CHANNEL, array );
    array.clear();

    if (!record.vecResistiveCurrentData.isEmpty())
    {
        array.resize(record.vecResistiveCurrentData.size() * sizeof(float));
        memcpy( array.data(), &record.vecResistiveCurrentData[0],  array.size() );
    }
    dbRecord.setData( ARRESTER_RESISTIVE_CURRENT_CHANNEL, array );
    array.clear();

    ArresterDB* pdb = (ArresterDB*)m_mapDBs[DB_ARRESTER];

    return pdb->updateArresterRecord( record.stPointArchiveInfo.strPointGUID,
                                      record.iautoId,
                                      dbRecord );
}

bool DBServer::addTempRecord(  quint8 aduid, quint8 channelid, float fTempData, ADUType eSensorType, QVector<TEMPRecord>* reportdata)
{
    PDS_SYS_INFO_LOG("addTempRecord");
    TEMPRecord record;
    record.aduId = QString::number(aduid);
    record.batteryInfo = 0;
    record.channelId = channelid;
    record.channelName = getChannelName(aduid, channelid);
    record.temprature = fTempData;
    record.isUpdate = 0;
    QList<PointArchiveInfo> tmp;
    m_configService->getPointArchivesFromChannelID(tmp, QString::number(aduid), channelid);
    if (!tmp.empty())
    {
        record.pointArchiveInfo = tmp.front();
    }
    record.recordId = 0;
    record.recordStringId = m_configService->getGUID();
    record.recordTime = QDateTime::currentDateTime();
    record.sensorType = eSensorType;
    PDS_SYS_INFO_LOG("add TempRecord finished");
    return addTempRecord(record, reportdata);
}
bool DBServer::addTempRecord(const TEMPRecord &record, QVector<TEMPRecord>* reportdata)
{
    PDS_SYS_INFO_LOG("addTempRecord");
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;

    if (reportdata != nullptr)
    {
        reportdata->clear();
    }

    dbRecord.setData( IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, record.recordTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.recordId );
    dbRecord.setData( SAMPLE_DATA_BATTERY, record.batteryInfo );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, record.signalstrength );
    dbRecord.setData( SAMPLE_NOISE_RATIO, record.noiseratio );

    dbRecord.setData( TEMP_DATA, serializeData(record));
    dbRecord.setData( TEMP_SENSOR_TYPE, record.sensorType);

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(record.aduId, record.channelId) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_TEMP];

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId + QString::number(i) );

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
//        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        int64_t currentId = -1;
        if (!pdb->addRecord(dbRecord, &currentId, nullptr))
        {
            PDS_SYS_ERR_LOG("add TempRecord failed, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());
        }

        if (reportdata != nullptr)
        {
            TEMPRecord stTEMPrecord = record;
            stTEMPrecord.globalId = dbRecord.value(IK_GLOBAL_ID).toULongLong();
            stTEMPrecord.autoId = currentId;
            stTEMPrecord.pointArchiveInfo = stPointArchiveInfo;
            stTEMPrecord.recordStringId = record.recordStringId + QString::number(i);
            reportdata->push_back(stTEMPrecord);
        }
    }
    PDS_SYS_INFO_LOG("add TempRecord finished, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());

    return true;
}

bool DBServer::addTempRecordAsync(const TEMPRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addTempRecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addTempRecordSync, record, fDataReport);

    return true;
}

bool DBServer::addTempRecordSync(const TEMPRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logInfo(QString("add temperature Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addTempRecordSync currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    if(hasTEMPRecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The temperature data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, record.recordTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.recordId );
    dbRecord.setData( SAMPLE_DATA_BATTERY, static_cast<quint8>(record.batteryInfo % 10));
    dbRecord.setData( SAMPLE_DATE_REMAIN_TIME, record.batteryInfo / 10);
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, record.signalstrength );
    dbRecord.setData( SAMPLE_NOISE_RATIO, record.noiseratio );

    dbRecord.setData( TEMP_DATA, serializeData(record));
    dbRecord.setData( TEMP_SENSOR_TYPE, record.sensorType);

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(record.aduId, record.channelId) );

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);

    SqliteDB* pdb = m_mapDBs[DB_TEMP];
    if(!pdb)
    {
        return false;
    }
    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId + QString::number(i) );

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
//        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        int64_t currentId = -1;
        if (!pdb->addRecord(dbRecord, &currentId, nullptr))
        {
            PDS_SYS_ERR_LOG("add temperature data failed, pointId: %s", stPointArchiveInfo.strPointGUID.toLatin1().data());
            return false;
        }

        if (fDataReport)
        {
            TEMPRecord stTEMPrecord = record;
            stTEMPrecord.globalId = dbRecord.value(IK_GLOBAL_ID).toULongLong();
            stTEMPrecord.autoId = currentId;
            stTEMPrecord.pointArchiveInfo = stPointArchiveInfo;
            stTEMPrecord.recordStringId = record.recordStringId + QString::number(i);

            PDSMPDATA reportData;
            reportData.data.setValue(stTEMPrecord);
            reportDataVector.append(reportData);
        }
    }
    logInfo("add temperature data result: true");

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("temperature data report...");
        fDataReport(reportDataVector);
    }

    return true;
}
bool DBServer::addHumidityRecord(const HumidityRecord &record, QVector<HumidityRecord>* reportdata)
{
    PDS_SYS_INFO_LOG("addHumidityRecord");
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;

    if (reportdata != nullptr)
    {
        reportdata->clear();
    }

    dbRecord.setData( IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, record.recordTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.recordId );
    dbRecord.setData( SAMPLE_DATA_BATTERY, record.batteryInfo );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, record.signalstrength );
    dbRecord.setData( SAMPLE_NOISE_RATIO, record.noiseratio );

    dbRecord.setData( HUMI_DATA, serializeData(record));
    dbRecord.setData(HUMI_SENSOR_TYPE, record.sensorType);

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(record.aduId, record.channelId) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_HUMI];

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId  + QString::number(i));

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
//        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        int64_t currentId = -1;
        if (!pdb->addRecord(dbRecord, &currentId, nullptr))
        {
            PDS_SYS_ERR_LOG("add HumidityRecord failed, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());
        }

        if (reportdata != nullptr)
        {
            auto reportrecord = record;
            reportrecord.pointArchiveInfo = listPointArchiveInfo[i];
            reportrecord.globalId = dbRecord.value(IK_GLOBAL_ID).toULongLong();
            reportrecord.autoId = currentId;
            reportrecord.recordStringId = record.recordStringId + QString::number(i);

            reportdata->push_back(reportrecord);
        }

    }

    //---test---
    if (!reportdata->empty())
    {
        PDS_SYS_INFO_LOG("reportdata->front().pointArchiveInfo.strPointGUID: %s", reportdata->front().pointArchiveInfo.strPointGUID.toLatin1().data());
    }//----end

    PDS_SYS_INFO_LOG("add HumidityRecord finished, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());

    return true;
}

bool DBServer::addHumidityRecordSync(const HumidityRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logInfo(QString("add Humidity Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addHumidityRecordSync currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    if(hasHumidityRecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The Humidity data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, record.recordTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.recordId );
    dbRecord.setData( SAMPLE_DATA_BATTERY, static_cast<quint8>(record.batteryInfo % 10));
    dbRecord.setData( SAMPLE_DATE_REMAIN_TIME, record.batteryInfo / 10);
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, record.signalstrength );
    dbRecord.setData( SAMPLE_NOISE_RATIO, record.noiseratio );

    dbRecord.setData( HUMI_DATA, serializeData(record));
    dbRecord.setData(HUMI_SENSOR_TYPE, record.sensorType);

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(record.aduId, record.channelId) );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_HUMI];
    if(!pdb)
    {
        return false;
    }

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);

    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId  + QString::number(i));

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
        //        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        int64_t currentId = -1;
        if (!pdb->addRecord(dbRecord, &currentId, nullptr))
        {
            PDS_SYS_ERR_LOG("add HumidityRecord failed, pointId: %s", stPointArchiveInfo.strPointGUID.toLatin1().data());
        }

        if (fDataReport)
        {
            HumidityRecord humidityData = record;
            humidityData.pointArchiveInfo = stPointArchiveInfo;
            humidityData.globalId = dbRecord.value(IK_GLOBAL_ID).toULongLong();
            humidityData.autoId = currentId;
            humidityData.recordStringId = record.recordStringId + QString::number(i);

            PDSMPDATA reportData;
            reportData.data.setValue(humidityData);
            reportDataVector.append(reportData);
        }
    }

    logInfo("add Humidity data result: true");

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("Humidity data report...");
        fDataReport(reportDataVector);
    }

    return true;
}

bool DBServer::addHumidityRecordAsync(const HumidityRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addHumidityRecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addHumidityRecordSync, record, fDataReport);

    return true;
}
bool DBServer::addTEVCountData(const QString& aduid, quint8 channelid, quint8 ucTEVMax, const QDateTime &dateTime)
{
    PDS_SYS_INFO_LOG("addTEVCountData");
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    auto rtn = addTEVRecord(convertToTevRecord(aduid, channelid, ucTEVMax, dateTime));
    if (!rtn)
    {
        PDS_SYS_INFO_LOG("add tev record failed");
    }

    PDS_SYS_INFO_LOG("addTEVCountData finished");

    return true;
}
bool DBServer::addTEMPCountData(const QString& aduid, quint8 channelid, float fTempterature, const QDateTime &dateTime )
{
    PDS_SYS_INFO_LOG("addTEMPCountData");
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( TEMP_TREND_COUNT, fTempterature );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_TEMP];

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, aduid, channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( TEMP_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        if (!pdb->addRecord( dbRecord ,TREND_COUNT_TABLE))
        {
            PDS_SYS_ERR_LOG("add addTEMPCountData failed, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());
        }
    }

    PDS_SYS_INFO_LOG("addTEMPCountData finished, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());

    return true;
}

bool DBServer::addHumidityRecord(quint8 aduid, quint8 channelid, float fHumidityData , ADUType eSensorType)
{
    PDS_SYS_INFO_LOG("add HumidityRecord start");
    HumidityRecord record;
    record.aduId = QString::number(aduid);
    record.batteryInfo = 0;
    record.channelId = channelid;
    record.channelName = getChannelName(aduid, channelid);
    record.humanity = fHumidityData;
    record.isUpdate = 0;
    QList<PointArchiveInfo> tmp;
    m_configService->getPointArchivesFromChannelID(tmp, QString::number(aduid), channelid);
    if (!tmp.empty())
    {
        record.pointArchiveInfo = tmp.front();
    }
    record.recordId = 0;
    record.recordStringId = m_configService->getGUID();
    record.recordTime = QDateTime::currentDateTime();
    record.sensorType = eSensorType;
    PDS_SYS_INFO_LOG("add HumidityRecord finished");
    addHumidityRecord(record, nullptr);
    return true;
}

bool DBServer::addFrostRawRecord(quint8 aduid, quint8 channelid,
                                 float fFrostRawData , ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);

    dbRecord.setData( FROST_RAW_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( FROST_RAW_DATA, fFrostRawData );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_FROST_RAW];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        pdb->addRecord( dbRecord );
    }

    return true;
}

bool DBServer::addFrostAtmRecord(quint8 aduid, quint8 channelid,
                                  float fFrostAtmData, ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);

    dbRecord.setData( FROST_ATM_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( FROST_ATM_DATA, fFrostAtmData );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_FROST_ATM];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        pdb->addRecord( dbRecord );
    }

    return true;
}

bool DBServer::addDewRawRecord(quint8 aduid, quint8 channelid,
                               float fDewRawData , ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);

    dbRecord.setData( DEW_RAW_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( DEW_RAW_DATA, fDewRawData );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_DEW_RAW];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        pdb->addRecord( dbRecord );
    }

    return true;
}

bool DBServer::addDewAtmRecord(quint8 aduid, quint8 channelid,
                                float fDewAtmData, ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);

    dbRecord.setData( DEW_ATM_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( DEW_ATM_DATA, fDewAtmData );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_DEW_ATM];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        pdb->addRecord( dbRecord );
    }

    return true;
}

bool DBServer::addMoistureRecord(quint8 aduid, quint8 channelid,
                                 float fMoistureData , ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);
    dbRecord.setData( MOISTURE_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( MOISTURE_DATA, fMoistureData );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_MOISTURE];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        pdb->addRecord( dbRecord );
    }

    return true;
}

bool DBServer::addPressAbsoRecoed(quint8 aduid, quint8 channelid,
                                  float fPressAbsoData , ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);
    dbRecord.setData( PRESS_ABSO_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( PRESS_ABSO_DATA, fPressAbsoData );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_PRESS_ABSO];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        pdb->addRecord( dbRecord );
    }

    return true;
}

bool DBServer::addPressNormRecord(quint8 aduid, quint8 channelid, float fPressNormData , ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);
    dbRecord.setData( PRESS_NORM_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( PRESS_NORM_DATA, fPressNormData );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_PRESS_NORM];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        pdb->addRecord( dbRecord );
    }

    return true;
}

bool DBServer::addDensityRecord(quint8 aduid, quint8 channelid,
                                float fDensityData , ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);
    dbRecord.setData( DENSITY_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( DENSITY_DATA, fDensityData );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_DENSITY];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        pdb->addRecord( dbRecord );
    }

    return true;
}

bool DBServer::addOxygenRecord(quint8 aduid, quint8 channelid,
                               float fOxygenData , ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);
    dbRecord.setData( OXYGEN_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( OXYGEN_DATA, fOxygenData );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_OXYGEN];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );

        pdb->addRecord( dbRecord );
    }

    return true;
}

bool DBServer::addSF6Record(quint8 aduid, quint8 channelid,
                            float fSF6Data , ADUType eSensorType)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;

    QDateTime dateTime = QDateTime::currentDateTime();
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );
    dbRecord.setData( SAMPLE_DATA_BATTERY, 0 );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, 0);
    dbRecord.setData( SAMPLE_NOISE_RATIO, 0);
    dbRecord.setData( SF6_SENSOR_TYPE, int(eSensorType) );
    dbRecord.setData( SF6_DATA, fSF6Data );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(aduid, channelid) );
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_SF6];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEMP_GLOBAL_ID_START)) );
        if (!pdb->addRecord(dbRecord))
        {
            PDS_SYS_ERR_LOG("addSF6Record failed, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());
        }
    }

    return true;
}

bool DBServer::addFrostRawCountData(quint8 aduid, quint8 channelid,
                      float fFrostRaw , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( FROST_RAW_TREND_COUNT, fFrostRaw );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_FROST_RAW];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( FROST_RAW_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addFrostAtmCountData(quint8 aduid, quint8 channelid,
                      float fFrostAtm , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( FROST_ATM_TREND_COUNT, fFrostAtm );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_FROST_ATM];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( FROST_ATM_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addDewRawCountData(quint8 aduid, quint8 channelid,
                      float fDewRaw , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( DEW_RAW_TREND_COUNT, fDewRaw );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_DEW_RAW];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( DEW_RAW_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addDewAtmCountData(quint8 aduid, quint8 channelid,
                      float fDewAtm , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( DEW_ATM_TREND_COUNT, fDewAtm );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_DEW_ATM];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( DEW_ATM_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addMoistureCountData(quint8 aduid, quint8 channelid,
                      float fMoisture , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( MOISTURE_TREND_COUNT, fMoisture );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_MOISTURE];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( MOISTURE_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addPressAbsoCountData(quint8 aduid, quint8 channelid,
                      float fPressAbso , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( PRESS_ABSO_TREND_COUNT, fPressAbso );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_PRESS_ABSO];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( PRESS_ABSO_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addPressNormCountData(quint8 aduid, quint8 channelid,
                      float fPressNorm , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( PRESS_NORM_TREND_COUNT, fPressNorm );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_PRESS_NORM];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( PRESS_NORM_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addDensityCountData(quint8 aduid, quint8 channelid,
                      float fDensity , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( DENSITY_TREND_COUNT, fDensity );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_DENSITY];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( DENSITY_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addOxygenCountData(quint8 aduid, quint8 channelid,
                      float fOxygen , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( OXYGEN_TREND_COUNT, fOxygen );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_OXYGEN];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( OXYGEN_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addSF6CountData(quint8 aduid, quint8 channelid,
                      float fSF6 , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( SF6_TREND_COUNT, fSF6 );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_SF6];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SF6_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addHumidityCountData(quint8 aduid, quint8 channelid,
                      float fHumidity , const QDateTime &dateTime)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, dateTime.toTime_t() );
    dbRecord.setData( HUMI_TREND_COUNT, fHumidity );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_HUMI];

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(aduid), channelid);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( HUMI_TREND_COUNT_POINT_GUID, stPointArchiveInfo.strPointGUID );

        pdb->addRecord( dbRecord ,TREND_COUNT_TABLE);
    }

    return true;
}

bool DBServer::addPDSMPDData(const PDSMPDATA &stPDSMPDATA)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    DataRecord stDataRecord;
    stDataRecord.setData( IK_RECORDTIME, stPDSMPDATA.daqtime.toString(DATETIME_FORMAT) );
    stDataRecord.setData( IK_RECORDTIME_T, stPDSMPDATA.daqtime.toTime_t() );
    stDataRecord.setData( SAMPLE_DATA_BATTERY, stPDSMPDATA.stBattery.battery );
    stDataRecord.setData( SAMPLE_SIGNAL_STRENGTH, stPDSMPDATA.stSignlInfo.signalstrength);
    stDataRecord.setData( SAMPLE_NOISE_RATIO, stPDSMPDATA.stSignlInfo.noiseratio);
    stDataRecord.setData( SAMPLE_DATA_DATA_ID, stPDSMPDATA.dataID );
    stDataRecord.setData( SAMPLE_DATA_POINT_GUID, stPDSMPDATA.testPointID );

    DataBaseServiceType eDB = getDatabaseType( stPDSMPDATA.lnType );

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        switch( stPDSMPDATA.lnType )
        {
        case MMXU:
        {
            METERDATA mtData = stPDSMPDATA.data.value<METERDATA>();
            stDataRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(METER_GLOBAL_ID_START)) );
            stDataRecord.setData( METER_DATA_CURRENT_A, mtData.aA[0] );
            stDataRecord.setData( METER_DATA_VOLTAGE_A, mtData.phvA[0] );
            stDataRecord.setData( METER_DATA_TOTAL_POWER, mtData.totW );
            stDataRecord.setData( METER_DATA_TOTAL_VAR, mtData.totVar );
        }
            break;
        case LVMETER:
        {
            LVMETERDATA lvData = stPDSMPDATA.data.value<LVMETERDATA>();
            stDataRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(LV_METER_GLOBAL_ID_START)) );
            stDataRecord.setData( LVMETER_DATA_CURRENT_A, lvData.data.aA[0] );
            stDataRecord.setData( LVMETER_DATA_VOLTAGE_A, lvData.data.phvA[0] );
            stDataRecord.setData( LVMETER_DATA_VOLTAGE_LINE_A, lvData.data.ppvAB[0] );
            stDataRecord.setData( LVMETER_DATA_TOTAL_POWER, lvData.data.totW );
            stDataRecord.setData( LVMETER_DATA_TOTAL_VAR, lvData.data.totVar );
            stDataRecord.setData( LVMETER_DATA_SWITCH_STATE, lvData.bPwr1 );
        }
            break;
        case CACC:
        {
            CACCDATA airData = stPDSMPDATA.data.value<CACCDATA>();
            stDataRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(CACC_GLOBAL_ID_START)) );
            stDataRecord.setData( CACC_DATA_SWITCH_STATE, airData.bOn );
            stDataRecord.setData( CACC_DATA_WORK_MODE, airData.eMode );
            stDataRecord.setData( CACC_DATA_TEMP, airData.fTmp );
            stDataRecord.setData( CACC_DATA_FAN, airData.nFan );
        }
            break;
        case SOUND:
        {
            SOUNDDATA sndData = stPDSMPDATA.data.value<SOUNDDATA>();
            stDataRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(NOISY_GLOBAL_ID_START)) );
            stDataRecord.setData( ENV_STATUS_VALUE, sndData.fSnd );
        }
            break;
        case SMOKE:
        {
            SMOKEDATA smkData = stPDSMPDATA.data.value<SMOKEDATA>();
            stDataRecord.setData(IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(SMOKE_GLOBAL_ID_START)));
            stDataRecord.setData(ENV_STATUS_VALUE, static_cast<int32_t>(smkData.bAlarm ? EnvStatus::ALARM : EnvStatus::NORMAL));
        }
            break;
        case SWLA:
        {
            SWLADATA swlaData = stPDSMPDATA.data.value<SWLADATA>();
            stDataRecord.setData(IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(WATER_LEACH_GLOBAL_ID_START)));
            stDataRecord.setData(ENV_STATUS_VALUE, static_cast<int32_t>(swlaData.bAlarm ? EnvStatus::ALARM : EnvStatus::NORMAL));
        }
            break;
        case WATER:
        {
            WATERLEVELDATA waterData = stPDSMPDATA.data.value<WATERLEVELDATA>();
            stDataRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(WATER_LEVEL_GLOBAL_ID_START)) );
            stDataRecord.setData( WATER_LEVEL_DATA_ALARM, waterData.fLevel );
        }
            break;
        case CLSL:
        case KFAN:
        {
            SWITCHDATA switchData = stPDSMPDATA.data.value<SWITCHDATA>();
            stDataRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(LOW_TENSION_SWITCH_GLOBAL_ID_START)) );
            stDataRecord.setData( LOW_TENSION_SWITCH_DATA_SWITCH_STATE, switchData.bOn );
        }
            break;
        case FIBER:
        {
            QVector<STMPDATA> vecTmp = stPDSMPDATA.data.value<QVector<STMPDATA> >();
            QByteArray arrayData;
            QVector<float> vecFiberData;
            int dataSize = vecTmp.size();
            for (int i = 0; i < dataSize; i++)
            {
                vecFiberData.append(vecTmp.at(i).fTmp);
            }
            if ( 0 != dataSize )
            {
                arrayData.resize( dataSize * sizeof( float ) );
                memcpy( arrayData.data(), &vecFiberData[0], arrayData.size() );
            }
            stDataRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(FIBER_TEMP_GLOBAL_ID_START)) );
            stDataRecord.setData( FIBER_TEMP_DATA_ARRAY, arrayData );
        }
            break;
        default:
            break;
        }

        return pdb->addRecord( stDataRecord );
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::addPDSMPDData, invalid db type: %d, invalid LnType: %d", eDB , stPDSMPDATA.lnType);
        return false;
    }
}
bool DBServer::getLastPDSMPDData(PDSMPDATA &stPDSMPDATA)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足

    bool bRet = false;
    DataBaseServiceType eDB = getDatabaseType( stPDSMPDATA.lnType );

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        DataRecord dbRecord = pdb->getLastRecord( stPDSMPDATA.testPointID );

        bRet = getPDSMPDRecord(dbRecord, stPDSMPDATA);
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::addPDSMPDData, invalid db type: %d, invalid LnType: %d", eDB , stPDSMPDATA.lnType);
        return false;
    }

    return bRet;
}

bool DBServer::addSf6StateRecord(const Sf6StateRecord &record, QVector<Sf6StateRecord> *reportdata)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    QString strChannelName = getChannelName(record.aduId, record.channelId);

    DataRecord dbRecord;

    if (reportdata != nullptr)
    {
        reportdata->clear();
    }

    dbRecord.setData( IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, record.recordTime.toTime_t() );
    dbRecord.setData( SF6_PRESSURE, record.pressure );
    dbRecord.setData( SF6_DENSITY, record.density );
    dbRecord.setData( SF6_TEMPRETURE, record.temprature );
    dbRecord.setData(SF6_SF6, record.sf6value);
    dbRecord.setData(SF6_O2, record.o2value);


    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.recordId );
    dbRecord.setData( SAMPLE_DATA_BATTERY, record.batteryInfo );
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, record.signalstrength );
    dbRecord.setData( SAMPLE_NOISE_RATIO, record.noiseratio );
    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, strChannelName );//建表字段

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_SF6_STATE];
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);
    qint64 autoId = 0;
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId + QString::number(i) );

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
//        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        auto globalId = pdb->getGlobalID(TEV_GLOBAL_ID_START);
        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEV_GLOBAL_ID_START)) );

        pdb->addRecord(dbRecord, &autoId, nullptr);

        if (reportdata != nullptr)
        {
            Sf6StateRecord sf6staterecord = record;
            sf6staterecord.pointArchiveInfo = stPointArchiveInfo;
            sf6staterecord.recordStringId = record.recordStringId + QString::number(i);
            sf6staterecord.globalId = globalId;
            sf6staterecord.autoId = autoId;
            reportdata->push_back(sf6staterecord);
        }

    }
    return true;
}

bool DBServer::addSF6StateRecordSync(const Sf6StateRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logInfo(QString("add SF6 Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addSF6StateRecordSync currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    if(hasSf6StateRecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The SF6 data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }

    SqliteDB* pdb = m_mapDBs[DB_SF6_STATE];
    if(!pdb)
    {
        logError("SF6 pdb is null.");
        return false;
    }

    if (!m_configService)
    {
        logError("m_configService has not been initialized!");
        return false;
    }

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, record.recordTime.toTime_t() );
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.recordId );
    dbRecord.setData( SAMPLE_DATA_BATTERY, static_cast<quint8>(record.batteryInfo % 10));
    dbRecord.setData( SAMPLE_DATE_REMAIN_TIME, record.batteryInfo / 10);
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, record.signalstrength );
    dbRecord.setData( SAMPLE_NOISE_RATIO, record.noiseratio );
    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName(record.aduId, record.channelId) );

    dbRecord.setData( SF6_PRESSURE, record.pressure );
    dbRecord.setData( SF6_DENSITY, record.density );
    dbRecord.setData( SF6_TEMPRETURE, record.temprature );
    dbRecord.setData( SF6_ENV_TEMPERATURE, record.envTemprature);
    dbRecord.setData( SF6_ALARM_STATUS, record.alarmStatus);
    dbRecord.setData( SF6_SUPERCAP_VOLTAGE, record.superCapvoltage);
    dbRecord.setData(SF6_SF6, record.sf6value);
    dbRecord.setData(SF6_O2, record.o2value);


    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);

    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId + QString::number(i) );

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(SF6_GLOBAL_ID_START)) );

        qint64 currentId = 0;
        pdb->addRecord(dbRecord, &currentId, nullptr);

        if (fDataReport)
        {
            Sf6StateRecord SF6StateRecordTmp = record;
            SF6StateRecordTmp.globalId = dbRecord.value(IK_GLOBAL_ID).toULongLong();;
            SF6StateRecordTmp.autoId = currentId;
            SF6StateRecordTmp.pointArchiveInfo = stPointArchiveInfo;
            SF6StateRecordTmp.recordStringId = record.recordStringId + QString::number(i);

            PDSMPDATA reportData;
            reportData.data.setValue(SF6StateRecordTmp);
            reportDataVector.append(reportData);
        }

    }

    logInfo("add SF6 data result: true");

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("SF6 data report...");
        fDataReport(reportDataVector);
    }

    return true;
}

bool DBServer::addSF6StateRecordASync(const Sf6StateRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addSF6StateRecordASync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addSF6StateRecordSync, record, fDataReport);

    return true;
}

bool DBServer::addEnvStatusRecord(const EnvStatusRecord &record, QVector<EnvStatusRecord> *reportdata)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    QString strChannelName = getChannelName(record.aduId, record.channelId);

    DataRecord dbRecord;

    if (reportdata != nullptr)
    {
        reportdata->clear();
    }

    dbRecord.setData(IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT));
    dbRecord.setData(IK_RECORDTIME_T, record.recordTime.toTime_t());
    dbRecord.setData(ENV_STATUS, static_cast<int32_t>(record.status));
    dbRecord.setData(ENV_STATUS_VALUE, record.value);


    dbRecord.setData(SAMPLE_DATA_RECORD_ID, record.recordId);
    dbRecord.setData(SAMPLE_DATA_BATTERY, record.batteryInfo);
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, record.signalstrength );
    dbRecord.setData( SAMPLE_NOISE_RATIO, record.noiseratio );
    dbRecord.setData(SAMPLE_DATA_IS_DATA_UPDATE, 0);

    dbRecord.setData(SAMPLE_DATA_CHANNEL_NAME, strChannelName);//建表字段

    SqliteDB* pdb = reinterpret_cast<SqliteDB*>(m_mapDBs[DB_ENV_STATUS]);
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);
    qint64 autoId = 0;
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId + QString::number(i) );

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
        //        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        auto globalId = pdb->getGlobalID(TEV_GLOBAL_ID_START);
        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEV_GLOBAL_ID_START)) );

        pdb->addRecord(dbRecord, &autoId, nullptr);

        if (reportdata != nullptr)
        {
            EnvStatusRecord waterloggingrecord = record;
            waterloggingrecord.pointArchiveInfo = stPointArchiveInfo;
            waterloggingrecord.recordStringId = record.recordStringId + QString::number(i);
            waterloggingrecord.globalId = globalId;
            waterloggingrecord.autoId = autoId;
            reportdata->push_back(waterloggingrecord);
        }

    }
    return true;
}

bool DBServer::addEnvStatusRecordSync(const EnvStatusRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logInfo(QString("add EnvStatus Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addEnvStatusRecordSync currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    if(hasWaterLoggingRecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The EnvStatus data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }

    QString strChannelName = getChannelName(record.aduId, record.channelId);

    DataRecord dbRecord;
    dbRecord.setData(IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT));
    dbRecord.setData(IK_RECORDTIME_T, record.recordTime.toTime_t());
    dbRecord.setData(ENV_STATUS, static_cast<int32_t>(record.status));
    dbRecord.setData(ENV_STATUS_VALUE, record.value);

    dbRecord.setData(SAMPLE_DATA_RECORD_ID, record.recordId);
    dbRecord.setData(SAMPLE_DATA_BATTERY, record.batteryInfo);
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, record.signalstrength );
    dbRecord.setData( SAMPLE_NOISE_RATIO, record.noiseratio );
    dbRecord.setData(SAMPLE_DATA_IS_DATA_UPDATE, 0);

    dbRecord.setData(SAMPLE_DATA_CHANNEL_NAME, strChannelName);//建表字段

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);

    SqliteDB* pdb = m_mapDBs[DB_ENV_STATUS];
    if(!pdb)
    {
        return false;
    }

    qint64 autoId = 0;
    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId + QString::number(i) );

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
        //        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        auto globalId = pdb->getGlobalID(TEV_GLOBAL_ID_START);
        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEV_GLOBAL_ID_START)) );

        if(!pdb->addRecord(dbRecord, &autoId, nullptr))
        {
            logError("add EnvStatus Data failed, pointId: ") << stPointArchiveInfo.strPointGUID;
            return false;
        }

        if (fDataReport)
        {
            EnvStatusRecord stEnvStatusData= record;
            stEnvStatusData.pointArchiveInfo = stPointArchiveInfo;
            stEnvStatusData.recordStringId = record.recordStringId + QString::number(i);
            stEnvStatusData.globalId = globalId;
            stEnvStatusData.autoId = autoId;

            PDSMPDATA reportData;
            reportData.data.setValue(stEnvStatusData);
            reportDataVector.append(reportData);
        }
    }

    logInfo("add EnvStatus Data true");

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("EnvStatus data report...");
        fDataReport(reportDataVector);
    }

    return true;
}

bool DBServer::addEnvStatusRecordAsync(const EnvStatusRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addEnvStatusRecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addEnvStatusRecordSync, record, fDataReport);

    return true;
}

bool DBServer::addHkVideoRecord(const HkVideoRecord &record, QVector<HkVideoRecord> *reportdata)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    QString strChannelName = getChannelName(record.aduId, record.channelId);

    DataRecord dbRecord;

    if (reportdata != nullptr)
    {
        reportdata->clear();
    }
    PDS_SYS_INFO_LOG("add hkvideo info, imagepath size: %d", record.imagePath.size());

    dbRecord.setData(IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT));
    dbRecord.setData(IK_RECORDTIME_T, record.recordTime.toTime_t());
    dbRecord.setData(HK_VIDEO_IMAGE_PATH, QJsonDocument(QJsonArray::fromStringList(record.imagePath)).toJson(QJsonDocument::Compact));
    dbRecord.setData(HK_CAPTURE_TYPE, record.captureType);
    dbRecord.setData(HK_MAX_TEMPRATURE, record.maxTemperature);
    dbRecord.setData(HK_AVG_TEMPRATURE, record.avgTemperature);
    dbRecord.setData(HK_MAX_TEMPRATURE_POSITION_X, record.maxTempraturePostionX);
    dbRecord.setData(HK_MAX_TEMPRATURE_POSITION_Y, record.maxTempraturePostionY);
    dbRecord.setData(HK_REGION_INFO, record.strRegionInfo);

    dbRecord.setData(SAMPLE_DATA_RECORD_ID, record.recordId);
    dbRecord.setData(SAMPLE_DATA_BATTERY, record.batteryInfo);
    dbRecord.setData( SAMPLE_SIGNAL_STRENGTH, record.signalstrength );
    dbRecord.setData( SAMPLE_NOISE_RATIO, record.noiseratio );
    dbRecord.setData(SAMPLE_DATA_IS_DATA_UPDATE, 0);

    dbRecord.setData(SAMPLE_DATA_CHANNEL_NAME, strChannelName);//建表字段

    SqliteDB* pdb = reinterpret_cast<SqliteDB*>(m_mapDBs[DB_HK_VIDEO]);
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (!m_configService)
    {
        PDS_SYS_WARNING_LOG("m_configService has not been initialized!");
        return false;
    }
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);
    qint64 autoId = 0;
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId + QString::number(i) );

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
        //        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        auto globalId = pdb->getGlobalID(TEV_GLOBAL_ID_START);
        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(TEV_GLOBAL_ID_START)) );

        pdb->addRecord(dbRecord, &autoId, nullptr);

        if (reportdata != nullptr)
        {
            auto reportrecord = record;
            reportrecord.pointArchiveInfo = stPointArchiveInfo;
            reportrecord.recordStringId = record.recordStringId + QString::number(i);
            reportrecord.globalId = globalId;
            reportrecord.autoId = autoId;
            reportdata->push_back(reportrecord);
        }

    }
    PDS_SYS_INFO_LOG("add hk video data end");
    return true;

}

/*************************************************
功能： 获取数据ID对应的数据记录
输入参数：
        strPointID -- 测点id
        recordTime -- 数据时间
        eChannelType -- 数据类型
        strFileName -- 文件名称
        strFileFullPath -- 文件名称全路径
返回值：
        数据记录
*************************************************************/
bool DBServer::getDataFilePath(const QString &strPointID, const QDateTime &recordTime, ADUChannelType eChannelType, QString &strFileName)
{
    bool bRet = false;
    if ((!strPointID.isEmpty()) && (recordTime.isValid()))
    {
        QString strSuffix = "";
        bRet = true;
        switch (eChannelType) {
        case CHANNEL_AE:
            //strSuffix = "_AE.t01";
            strSuffix = "_01.dat";
            break;
        case CHANNEL_TEV:
            //strSuffix = "_TEV.t28";
            strSuffix = "_02.dat";
            break;
        case CHANNEL_UHF:
            //strSuffix = "_UHF.t09";
            strSuffix = "_03.dat";
            break;
        case CHANNEL_HFCT:
            //strSuffix = "_HFCT.t11";
            strSuffix = "_04.dat";
            break;
        case CHANNEL_MECH:
            //strSuffix = "_MECH.t15";
            strSuffix = "_05.dat";
            break;
        default:
            bRet = false;
            break;
        }

        strFileName = strPointID + recordTime.toString("_yyyyMMddhhmmss") + strSuffix;
    }

    return bRet;
}

bool DBServer::getDatafileFullPath(const QString &strPointID, const QDateTime &recordTime, ADUChannelType eChannelType, QString &strFileFullPath)
{
    QString strPathfile;
    bool bRet = getDataFilePath( strPointID,recordTime, eChannelType, strPathfile );
    strFileFullPath = g_strDataFileDir + "/" + strPathfile;
    return bRet;
}

/*************************************************
功能： 获取某类型数据库数据起终序号
输入参数：
        eChanneltype -- 通道类型
输出参数：
        startIndex -- 起始数据序号
        endIndex -- 终止数据序号
返回：操作结果
*************************************************************/
bool DBServer::getRecordIndex(ADUChannelType eChanneltype, quint64 &startIndex, quint64 &endIndex)
{
    DataBaseServiceType eDB = getDatabaseType(eChanneltype);

    if (getRecordIndex(eDB, startIndex, endIndex))
    {
        return true;
    }

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        return pdb->getRecordIndex(startIndex, endIndex);
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::getRecordIndex, invalid db type: %d", eDB);
        return false;
    }
}

/*************************************************
功能： 设置数据已上传
输入参数：
   eChannelType -- 数据类型
   strRecordId -- 记录id
返回值： 操作结果
*************************************************************/
bool DBServer::setRecordIsUpdate(ADUChannelType eChannelType, quint64 strGlobalId)
{
    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        return pdb->setRecordIsUpdate(strGlobalId);
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::setRecordIsUpdate, invalid db type: %d", eDB);
        return false;
    }
}

/*************************************************
功能： 获取某表的autoid的最大值
输入参数：
    strChannelName -- 通道名
输入参数：
    iMaxAutoId -- 最大自增键值
返回值： 操作结果
*************************************************************/
void DBServer::getAutoIdInfo(ADUChannelType eChannelType, const QString &strPointID, int &iMaxAutoId)
{
    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    if (getAutoIdInfo(eDB, strPointID, iMaxAutoId))
    {
        return;
    }

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        pdb->getAutoIdInfo(strPointID, iMaxAutoId);
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::setRecordIsUpdate, invalid db type: %d", eDB);
    }
}

/*************************************************
    功能： 获取时间范围内的autoID列表
    输入参数：
        eChannelType -- 通道类型
        strPointID --测点ID
        startTime -- 开始时间
        endTime -- 结束时间
    输入参数：
        listAutoID -- autoid列表
    返回值： void
*************************************************************/
void DBServer::getAutoIdInfo(ADUChannelType eChannelType, const QString &strPointID, QList<int> &listAutoID, QDateTime startTime, QDateTime endTime)
{
    if (!startTime.isValid())
    {
        startTime = QDateTime::fromString("2000","yyyy");
    }
    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];
        if (!endTime.isValid())
        {
            endTime = QDateTime::fromTime_t(pdb->lastRecordTimeT(strPointID)).addSecs(20);
        }

        pdb->getAutoIdInfo(strPointID, listAutoID, startTime, endTime);
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::getAutoIdInfo, invalid db type: %d", eDB);
    }
}

/*************************************************
功能： 获取某表的autoid的最大值
输入参数：
    strChannelName -- 通道名
输入参数：
    iMaxAutoId -- 最大自增键值
    iMinAutoId -- 最小自增键值
返回值： 操作结果
*************************************************************/
void DBServer::getAutoIdInfo(ADUChannelType eChannelType, const QString &strPointID, int &iMaxAutoId, int &iMinAutoId, int &maxRecordTimeT, int &minRecordTimeT)
{
    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    if (getAutoIdInfo(eDB, strPointID, iMaxAutoId, iMinAutoId, maxRecordTimeT, minRecordTimeT))
    {
        return;
    }

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        pdb->getAutoIdInfo(strPointID, iMaxAutoId, iMinAutoId, maxRecordTimeT, minRecordTimeT);
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::setRecordIsUpdate, invalid db type: %d", eDB);
    }
}

/*************************************************
功能： 获取测点的电池电量信息
输入参数：
    eChannelType -- 通道类型
    strPointID -- 测点id
输入参数：
    iBatrery -- 电池电量
返回值： 操作结果
*************************************************************/
bool DBServer::getPointBatteryInfo(ADUChannelType eChannelType, const QString &strPointID, int &ucBatrery)
{
    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        return pdb->getPointBatteryInfo(strPointID, ucBatrery);
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::setRecordIsUpdate, invalid db type: %d", eDB);
        return false;
    }
}

/*************************************************
功能： 最新一条记录
输入参数：
        strChannelName -- 通道名称
输出参数：
        true -- 成功
        else -- 记录不存在
返回值：
        局放数据记录
*************************************************************/
AERecord DBServer::lastAERecord( const QString &strPointID, bool* /*pbValid*/ )
{
    PDS_SYS_INFO_LOG("DBServer::lastAERecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::lastAERecord end");});
    common::DataWrapper tmpdata;
    Status status = Status::OK();
    ::AERecord rtn;
    if (!(status = m_storageengine->getLastRecord(strPointID, &tmpdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    auto unwrapData = tmpdata.getData<common::PdAeData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdAeData(strPointID, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}
TEVRecord DBServer::lastTEVRecord( const QString &strPointID, bool* /*pbValid*/ )
{
    PDS_SYS_INFO_LOG("DBServer::lastTEVRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::lastTEVRecord end");});
    common::DataWrapper tmpdata;
    Status status = Status::OK();
    ::TEVRecord rtn;
    if (!(status = m_storageengine->getLastRecord(strPointID, &tmpdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    auto unwrapData = tmpdata.getData<common::PdTevData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdTevData(strPointID, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}
PRPSRecord DBServer::lastUHFPRPSRecord( const QString &strPointID, bool* /*pbValid*/ )
{
    PDS_SYS_INFO_LOG("DBServer::lastUHFPRPSRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::lastUHFPRPSRecord end");});
    common::DataWrapper tmpdata;
    Status status = Status::OK();
    ::PRPSRecord rtn;
    if (!(status = m_storageengine->getLastRecord(strPointID, &tmpdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    auto unwrapData = tmpdata.getData<common::PdUhfData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdUhfData(strPointID, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}
PRPSRecord DBServer::lastHFCTPRPSRecord( const QString &strPointID, bool* /*pbValid*/ )
{
    PDS_SYS_INFO_LOG("DBServer::lastHFCTPRPSRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::lastHFCTPRPSRecord end");});
    common::DataWrapper tmpdata;
    Status status = Status::OK();
    ::PRPSRecord rtn;
    if (!(status = m_storageengine->getLastRecord(strPointID, &tmpdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    auto unwrapData = tmpdata.getData<common::PdHfctData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdHfctData(strPointID, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}
TEMPRecord DBServer::lastTEMPRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = static_cast<ENVDB*>(m_mapDBs[DB_TEMP]);
    if (!pdb)
    {
        PDS_SYS_ERR_LOG("DBServer::lastTEMPRecord, invalid db type: %d", DB_TEMP);
        return TEMPRecord();
    }

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getTEMPRecord( dbRecord );
}
HumidityRecord DBServer::lastHumidityRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_HUMI];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getHumidityRecord( dbRecord );
}
FrostRawRecord DBServer::lastFrostRawRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_FROST_RAW];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getFrostRawRecord( dbRecord );
}
FrostAtmRecord DBServer::lastFrostAtmRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_FROST_ATM];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getFrostAtmRecord( dbRecord );
}
DewRawRecord DBServer::lastDewRawRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_DEW_RAW];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getDewRawRecord( dbRecord );
}
DewAtmRecord DBServer::lastDewAtmRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_DEW_ATM];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getDewAtmRecord( dbRecord );
}
MoistureRecord DBServer::lastMoistureRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_MOISTURE];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getMoistureRecord( dbRecord );
}
PressAbsoRecord DBServer::lastPressAbsoRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_PRESS_ABSO];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getPressAbsoRecord( dbRecord );
}
PressNormRecord DBServer::lastPressNormRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_PRESS_NORM];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getPressNormRecord( dbRecord );
}
DensityRecord DBServer::lastDensityRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_DENSITY];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getDensityRecord( dbRecord );
}
OxygenRecord DBServer::lastOxygenRecord( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_OXYGEN];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getOxygenRecord( dbRecord );
}
SF6Record DBServer::lastSF6Record( const QString &strChannelName, bool* pbValid )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_SF6];

    DataRecord dbRecord = pdb->lastRecord( strChannelName );

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getSF6Record( dbRecord );
}

Sf6StateRecord DBServer::lastSf6StateRecord(const QString &pointId, bool *pbValid) const
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_SF6_STATE];

    DataRecord dbRecord = pdb->lastRecord(pointId);

    if( NULL != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getSf6StateRecord(dbRecord);
}

EnvStatusRecord DBServer::lastEnvStatusRecord(const QString &pointId, bool *pbValid) const
{
    auto pdb = reinterpret_cast<PDDB*>(m_mapDBs[DB_ENV_STATUS]);

    DataRecord dbRecord = pdb->lastRecord(pointId);

    if( nullptr != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getEnvStatusRecord(dbRecord);
}

HkVideoRecord DBServer::lastHkVideoRecord(const QString &pointId, bool *pbValid) const
{
    PDDB* pdb = (PDDB*)m_mapDBs[DB_HK_VIDEO];

    DataRecord dbRecord = pdb->lastRecord(pointId);

    if( nullptr != pbValid )
    {
        *pbValid = dbRecord.isValid();
    }

    return getHkVideoRecord(dbRecord);
}

quint64 DBServer::lastAERecordID(const QString &strChannelName)
{
    uint64_t autoId = 0;
    uint64_t globalId = 0;
    auto status = m_storageengine->getAutoIndexAndGlobalIndex(strChannelName, QDateTime(), &autoId, &globalId);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    return autoId;
}
quint64 DBServer::lastTEVRecordID(const QString &strChannelName)
{
    uint64_t autoId = 0;
    uint64_t globalId = 0;
    auto status = m_storageengine->getAutoIndexAndGlobalIndex(strChannelName, QDateTime(), &autoId, &globalId);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    return autoId;
}
quint64 DBServer::lastUHFRecordID(const QString &strChannelName)
{
    uint64_t autoId = 0;
    uint64_t globalId = 0;
    auto status = m_storageengine->getAutoIndexAndGlobalIndex(strChannelName, QDateTime(), &autoId, &globalId);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    return autoId;
}
quint64 DBServer::lastHFCTRecordID(const QString &strChannelName)
{
    uint64_t autoId = 0;
    uint64_t globalId = 0;
    auto status = m_storageengine->getAutoIndexAndGlobalIndex(strChannelName, QDateTime(), &autoId, &globalId);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    return autoId;
}

/*************************************************
功能： 获取最新一条记录的时间
输入参数：
        strADUID -- 前端ID
        eChannelType -- 通道类型
输出参数：
        dateTime -- 最新一条记录的时间
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::getLastRecordDateTime(const QString &strADUID, QDateTime &dateTime)
{
    ADUChannelType eType;
    m_configService->getChannelTypeFromID(strADUID, 0,eType);
    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, 0);
    if (listPointArchiveInfo.empty())
    {
        PDS_SYS_ERR_LOG("cannot find pointid");
        return false;
    }

    auto pointId = listPointArchiveInfo.front().strPointGUID;
    DataBaseServiceType eDB = getDatabaseType( eType );

    if (getLastRecordDateTimeInEngine(eDB, pointId, &dateTime))
    {
        return true;
    }

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        bool bRet = false;
        bRet = pdb->getLastRecordDateTime(pointId,  dateTime);
        return bRet;
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::getLastRecordDateTime, invalid db type: %d", eDB);
        return false;
    }
}

/*************************************************
功能： 获取最新一条记录的ID
输入参数：
        strPointId -- 前端ID
        eChannelType -- 通道类型
返回值：
        最新一条记录的ID
*************************************************************/
int DBServer::lastRecordId(const QString &strPointId, ADUChannelType eChannelType)
{
    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    int32_t lastId = 0;

    if (lastRecordId(eDB, strPointId, &lastId))
    {
        return lastId;
    }

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        return pdb->lastRecordID( strPointId );
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::lastRecordId, invalid db type: %d", eDB);
        return 0;
    }
}

int64_t DBServer::totalDataNumInTable(const QString &strPointId, ADUChannelType eChannelType)
{
    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    int64_t num = 0;
    if (getTotalTableDataNumInEngine(eDB, strPointId, num))
    {
        return num;
    }

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        return pdb->getTotalTableDataNum( strPointId );
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::lastRecordId, invalid db type: %d", eDB);
        return 0;
    }
}

/*************************************************
功能： 获取数据前后一条数据的id
输入参数：
        strPointId -- 前端ID
        eChannelType -- 通道类型
        iRecord -- 数据的id
输出参数：
        iPreDataID -- 前一条数据的id
        iNextRecordID -- 下一条数据的id
*************************************************************/
void DBServer::nearbyRecordId(const QString &strPointId, ADUChannelType eChannelType, int iRecord, int &iPreDataID, int &iNextRecordID)
{
    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    if (nearbyRecordId(eDB, strPointId, iRecord, &iPreDataID, &iNextRecordID))
    {
        return;
    }

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        pdb->nearbyRecordId( strPointId, iRecord, iPreDataID, iNextRecordID);
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::nearbyRecordId, invalid db type: %d", eDB);
    }
}

/*************************************************
功能： 判断在timeBegin和timeEnd时间范围内，数据库中是否存在前端号为ucAddr、记录号为usMechRecordID的局放记录数据
输入参数：
        strADUID：前端ID
        ucChannelID: 通道ID
        usRecordID：记录ID
        timeBegin：起始日期
        timeEnd：停止日期
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::hasAERecord(const QString &strADUID, quint8 ucChannelID, quint16 usRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = m_storageengine->hasRecordNumberInTimePeriod(listPointArchiveInfo.front().strPointGUID, usRecordID, timeBegin, timeEnd);
    };
    return bRet;
}
bool DBServer::hasTEVRecord(const QString &strADUID, quint8 ucChannelID, quint16 usRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = m_storageengine->hasRecordNumberInTimePeriod(listPointArchiveInfo.front().strPointGUID, usRecordID, timeBegin, timeEnd);
    };
    return bRet;
}
bool DBServer::hasUHFRecord(const QString &strADUID, quint8 ucChannelID, quint16 usRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = m_storageengine->hasRecordNumberInTimePeriod(listPointArchiveInfo.front().strPointGUID, usRecordID, timeBegin, timeEnd);
    };
    return bRet;
}
bool DBServer::hasHFCTRecord(const QString &strADUID, quint8 ucChannelID, quint16 usRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = m_storageengine->hasRecordNumberInTimePeriod(listPointArchiveInfo.front().strPointGUID, usRecordID, timeBegin, timeEnd);
    };
    return bRet;
}

bool DBServer::hasTEVPRPSRecord(const QString &strADUID, quint8 ucChannelID, quint16 usRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = m_storageengine->hasRecordNumberInTimePeriod(listPointArchiveInfo.front().strPointGUID, usRecordID, timeBegin, timeEnd);
    };
    return bRet;
}

bool DBServer::hasUHFRecord(const QString &strADUID, quint8 ucChannelID, const QDateTime &recordTime)
{
    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = m_storageengine->hasRecordInTimePeriod(listPointArchiveInfo.front().strPointGUID, recordTime, recordTime);
    };
    return bRet;
}

/*************************************************
功能： 获取在在timeBegin和timeEnd时间范围内，数据库中RecordID的列表，用于比较数据是否已入库
输入参数：
        strADUID：前端ID
        ucChannelID: 通道ID
        timeBegin：起始日期
        timeEnd：停止日期
输出参数：
        listRecordID -- RecordID的列表
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::getRecordIDListFromDateTime(const QString &strADUID, quint8 ucChannelID, const QDateTime &timeBegin, const QDateTime &timeEnd, QList<quint16> &listRecordID)
{
    ADUChannelType eChannelType;
    m_configService->getChannelTypeFromID( strADUID , ucChannelID, eChannelType);

    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    QList<PointArchiveInfo> listPointArchiveInfo;

    if (!m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID) || listPointArchiveInfo.empty())
    {
        PDS_SYS_ERR_LOG("cannot find pointid");
        return false;
    }

    auto pointId = listPointArchiveInfo.front().strPointGUID;

    if (getRecordIDListFromDateTimeInEngine(eDB, pointId, timeBegin, timeEnd, &listRecordID))
    {
        return true;
    }

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        bool bRet = false;
        bRet = pdb->getRecordIDListFromDateTime(pointId, timeBegin, timeEnd, listRecordID);

        return bRet;
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::getRecordIDListFromDateTime, invalid db type: %d", eDB);
        return false;
    }
}

bool DBServer::getPDSMPDataAtTimeMoment(const QString &strPointID, const QDateTime &timeMoment, PDSMPDATA &pData)
{
    bool getRet = false;    //获取数据结果
    QList<ADUChannelType> typeList =  m_configService->getChannelTypeListFromPoint(strPointID);
    if(typeList.isEmpty())
    {
        return getRet;
    }
    ADUChannelType eChannelType = typeList.first();
    DataBaseServiceType eDB = getDatabaseType( eChannelType );
    QSet<DataBaseServiceType> inStorage{DB_AE,DB_TEV,DB_UHFPRPS,DB_HFCTPRPS,DB_TEVPRPS};

    common::DataWrapper tmpdatas;
    DataRecord pointDataRecord;
    //数据查询 AE TEV UHFPRPS HFCTPRPS 通过m_storageengine查询
    if(inStorage.contains(eDB))
    {
        auto status = m_storageengine->getRecordAtTimeMoment(strPointID,timeMoment,&tmpdatas);
        if(!status.ok())
        {
            PDS_SYS_ERR_LOG("%s",status.ToString().c_str());
            return getRet;
        }
    }
    else
    {
        if(m_mapDBs.contains( eDB ))
        {
            SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];
            getRet = pdb->getRecord(strPointID,timeMoment,pointDataRecord);
            if(!getRet)
            {
                PDS_SYS_ERR_LOG("DBServer::getPDSMPDataAtTimeMoment pdb->getRecord failed");
                return getRet;
            }
        }
        else
        {
            PDS_SYS_WARNING_LOG("DBServer::getPDSMPDataAtTimeMoment, invalid db type: %d", eDB);
            return getRet;
        }
    }
    //数据转换
    Status status = Status::OK();
    switch(eChannelType)
    {
    case CHANNEL_AE:
    {
        AERecord rtn;
        auto unwrapData = tmpdatas.getData<common::PdAeData>();
        if (!unwrapData)
        {
            PDS_SYS_ERR_LOG("unwrap data failed");
        }
        else
        {
            if (!(status = DataTypeConvertionUtils::convertFromPdAeData(strPointID, *unwrapData, &rtn)).ok())
            {
                PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            }
            else
            {
                pData.lnType = SPDC_AE;
                pData.data.setValue(rtn);
                getRet = true;
            }
        }
    }
        break;
    case CHANNEL_TEV:
    {
        TEVRecord rtn;
        auto unwrapData = tmpdatas.getData<common::PdTevData>();
        if (!unwrapData)
        {
            PDS_SYS_ERR_LOG("unwrap data failed");
        }
        else
        {
            if (!(status = DataTypeConvertionUtils::convertFromPdTevData(strPointID, *unwrapData, &rtn)).ok())
            {
                PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            }
            else
            {
                pData.lnType = SPDC_TEV;
                pData.data.setValue(rtn);
                getRet = true;
            }
        }
    }
        break;
    case CHANNEL_UHF:
    {
        PRPSRecord rtn;
        auto unwrapData = tmpdatas.getData<common::PdUhfData>();
        if (!unwrapData)
        {
            PDS_SYS_ERR_LOG("unwrap data failed");
        }
        else
        {
            if (!(status = DataTypeConvertionUtils::convertFromPdUhfData(strPointID, *unwrapData, &rtn)).ok())
            {
                PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            }
            else
            {
                pData.lnType = SPDC_UHF;
                pData.data.setValue(rtn);
                getRet = true;
            }
        }
    }
        break;
    case CHANNEL_HFCT:
    {
        PRPSRecord rtn;
        auto unwrapData = tmpdatas.getData<common::PdHfctData>();
        if (!unwrapData)
        {
            PDS_SYS_ERR_LOG("unwrap data failed");
        }
        else
        {
            if (!(status = DataTypeConvertionUtils::convertFromPdHfctData(strPointID, *unwrapData, &rtn)).ok())
            {
                PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            }
            else
            {
                pData.lnType = SPDC_HFCT;
                pData.data.setValue(rtn);
                getRet = true;
            }
        }
    }
        break;
    case CHANNEL_MECH:
    {
        MechDefine::MechISRecord mechRecord;
        mechRecord = getMECHRecord(pointDataRecord);

        pData.lnType = MECH;
        pData.data.setValue(mechRecord);
        getRet = true;
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        TEMPRecord tempData;
        tempData =  getTEMPRecord(pointDataRecord);
        pData.lnType = STMP;
        pData.data.setValue(tempData);
        getRet = true;
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        HumidityRecord humidityData;
        humidityData =  getHumidityRecord(pointDataRecord);
        pData.lnType = THUM;
        pData.data.setValue(humidityData);
        getRet = true;
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_ARRESTER_U:
    {
        ArresterRecord arresterData;
        arresterData =  getArresterRecord(pointDataRecord);
        pData.lnType = ZSAR;
        pData.data.setValue(arresterData);
        getRet = true;
    }
        break;
    case CHANNEL_GROUNDDINGCURRENT:
    {
        ArresterRecord arresterData;
        arresterData =  getArresterRecord(pointDataRecord);
        pData.lnType = SPTR;
        pData.data.setValue(arresterData);
        getRet = true;
    }
        break;
    case CHANNEL_LEAKAGECURRENT:
    {
        ArresterRecord arresterData;
        arresterData =  getArresterRecord(pointDataRecord);
        pData.lnType = LEAK;
        pData.data.setValue(arresterData);
        getRet = true;
    }
        break;
    case CHANNEL_VIBRATION:
    {
        VibrationRecord virbrationData;
        virbrationData =  getVibrationRecord(pointDataRecord);
        pData.lnType = TVBR;
        pData.data.setValue(virbrationData);
        getRet = true;
    }
        break;

    //微水数据库:
    case CHANNEL_FROST_RAW:
    {

    }
        break;

    case CHANNEL_FROST_ATM:
    {

    }
        break;

    case CHANNEL_DEW_RAW:
    {

    }
        break;

    case CHANNEL_DEW_ATM:
    {

    }
        break;

    case CHANNEL_MOISTURE:
    {

    }
        break;

    case CHANNEL_PRESS_ABSO:
    {

    }
        break;

    case CHANNEL_PRESS_NORM:
    {

    }
        break;

    case CHANNEL_DENSITY:
    {

    }
        break;

    case CHANNEL_OXYGEN:
    {

    }
        break;

    case CHANNEL_SF6:
    {
        Sf6StateRecord tmpData;
        tmpData =  getSf6StateRecord(pointDataRecord);
        pData.lnType = SIMG;
        pData.data.setValue(tmpData);
        getRet = true;
    }
        break;
    case CHANNEL_LOWTENSVG:
    {
        pData.lnType = MMXU;
        getRet = getPDSMPDRecord(pointDataRecord,pData);
    }
        break;
    case CHANNEL_TENSION:
    {
        pData.lnType = LVMETER;
        getRet = getPDSMPDRecord(pointDataRecord,pData);
    }
        break;
    case CHANNEL_AIR_CONDITIONER:
    {
        pData.lnType = CACC;
        getRet = getPDSMPDRecord(pointDataRecord,pData);
    }
        break;
    case CHANNEL_NOISE:
    case CHANNEL_NOISE_SH:
    {
        //pData.lnType = SOUND;
        //getRet = getPDSMPDRecord(pointDataRecord,pData);
        EnvStatusRecord tmpData;
        tmpData =  getEnvStatusRecord(pointDataRecord);
        pData.lnType = SOUND;
        pData.data.setValue(tmpData);
        getRet = true;
    }
        break;
    case CHANNEL_FIREWORKS_ALARM:
    {
        //pData.lnType = SMOKE;
        //getRet = getPDSMPDRecord(pointDataRecord,pData);
        EnvStatusRecord tmpData;
        tmpData =  getEnvStatusRecord(pointDataRecord);
        pData.lnType = SMOKE;
        pData.data.setValue(tmpData);
        getRet = true;
    }
        break;
    case CHANNEL_WATER:
    case CHANNEL_FLOOD:
    {
        //pData.lnType = SWLA;
        //getRet = getPDSMPDRecord(pointDataRecord,pData);
        EnvStatusRecord tmpData;
        tmpData =  getEnvStatusRecord(pointDataRecord);
        pData.lnType = SWLA;
        pData.data.setValue(tmpData);
        getRet = true;
    }
    case CHANNEL_IR_DETECTION:
    {
        EnvStatusRecord tmpData;
        tmpData =  getEnvStatusRecord(pointDataRecord);
        pData.lnType = SBAL;
        pData.data.setValue(tmpData);
        getRet = true;
    }
        break;
    case CHANNEL_LIGHT:
    {
        //pData.lnType = CLSL;
        //getRet = getPDSMPDRecord(pointDataRecord,pData);
        EnvStatusRecord tmpData;
        tmpData =  getEnvStatusRecord(pointDataRecord);
        pData.lnType = CLSL;
        pData.data.setValue(tmpData);
        getRet = true;
    }
    case CHANNEL_FAN:
    {
        //pData.lnType = KFAN;
        //getRet = getPDSMPDRecord(pointDataRecord,pData);
        EnvStatusRecord tmpData;
        tmpData =  getEnvStatusRecord(pointDataRecord);
        pData.lnType = KFAN;
        pData.data.setValue(tmpData);
        getRet = true;
    }
        break;
    case CHANNEL_LIQUID_LEVEL:
    {
        pData.lnType = WATER;
        getRet = getPDSMPDRecord(pointDataRecord,pData);
    }
        break;
    case CHANNEL_OPTICAL_TEMP:
    {
        pData.lnType = FIBER;
        getRet = getPDSMPDRecord(pointDataRecord,pData);
    }
        break;

    case CHANNEL_SF6_GAS:
    {
        /*SF6Record tmpData;
        tmpData =  getSF6Record(pointDataRecord);
        pData.lnType = SIMG;
        pData.data.setValue(tmpData);
        getRet = true;*/
        Sf6StateRecord tmpData;
        tmpData =  getSf6StateRecord(pointDataRecord);
        pData.lnType = SIMG;
        pData.data.setValue(tmpData);
        getRet = true;
    }
        break;

    case CHANNEL_HIKVIDEO:
    {
        HkVideoRecord tmpData;
        tmpData =  getHkVideoRecord(pointDataRecord);
        pData.lnType = CAMERA;
        pData.data.setValue(tmpData);
        getRet = true;
    }
        break;
    default:
        break;
    }
    return getRet;
}

bool DBServer::getPDSMPDataListByDateTime(ADUChannelType eChannelType, const QString &pointId, \
                                          const QDateTime &timeBegin, const QDateTime &timeEnd, \
                                          QList<PDSMPDATA> &pDataList)
{
    bool getRet = false;
    DataBaseServiceType eDB = getDatabaseType(eChannelType);
    DataRecordList dataRecordList;
    if(m_mapDBs.contains(eDB))
    {
        SqliteDB* pdb = m_mapDBs[eDB];
        getRet = pdb->getRecords(pointId,timeBegin,timeEnd,dataRecordList);
        if(getRet)
        {
            for(const DataRecord& data: dataRecordList)
            {
                PDSMPDATA pdData;
                switch (eChannelType)
                {
                case CHANNEL_LOWTENSVG:
                    pdData.lnType = MMXU;
                    break;
                case CHANNEL_TENSION:
                    pdData.lnType = LVMETER;
                    break;
                case CHANNEL_AIR_CONDITIONER:
                    pdData.lnType = CACC;
                    break;
                case CHANNEL_OPTICAL_TEMP:
                    pdData.lnType = FIBER;
                    break;
                case CHANNEL_HIKVIDEO:
                    pdData.lnType = CAMERA;
                    break;
                default:
                    break;
                }
                if(getPDSMPDRecord(data,pdData))
                {
                    pDataList.append(pdData);
                    getRet = true;
                }
            }
        }
        else
        {
            PDS_SYS_ERR_LOG("DBServer::getPDSMPDataListByDateTime pdb->getRecords failed");
        }
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::getPDSMPDataListByDateTime, invalid db type: %d", eDB);
    }
    return getRet;
}

bool DBServer::getRecordIDAndTimeListFromDateTimeInEngine(QString strPointID, const QDateTime &timeBegin, const QDateTime &timeEnd,
                                                          QList<quint16> &recordIDList, QList<QDateTime> &recordTimeList)
{
    bool bRet = false;

    recordIDList.clear();
    recordTimeList.clear();

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordInTimePeriod(strPointID, timeBegin, timeEnd, &tmpdatas);
    if (!status.ok())
    {
        return bRet;
    }

    std::transform(tmpdatas.cbegin(), tmpdatas.cend(), std::back_inserter(recordIDList),
                   [](const common::DataWrapper& itrValue){
        return static_cast<quint16>(itrValue.getData()->recordId.toInt());
    });
    std::transform(tmpdatas.cbegin(), tmpdatas.cend(), std::back_inserter(recordTimeList),
                   [](const common::DataWrapper& itrValue){
        return itrValue.getData()->getRecordTime();
    });

    if( !recordIDList.isEmpty() && ( recordTimeList.size() == recordIDList.size() ) )
    {
        bRet = true;
    }

    return bRet;
}

bool DBServer::getAutoIDAndRecordTimeListForTimePeriod(QString strPointID, const QDateTime &timeBegin, const QDateTime &timeEnd, \
                                                       QList<qint64> &autoIDList, QList<QDateTime> &recordTimeList)
{
    bool bRet = false;

    autoIDList.clear();
    recordTimeList.clear();

    QList<ADUChannelType> typeList =  m_configService->getChannelTypeListFromPoint(strPointID);
    if(typeList.isEmpty())
    {
        return bRet;
    }

    ADUChannelType eChannelType = typeList.first();
    DataBaseServiceType eDBType = getDatabaseType( eChannelType );
    QSet<DataBaseServiceType> inStorage{DB_AE,DB_TEV,DB_UHFPRPS,DB_HFCTPRPS,DB_TEVPRPS};

    //数据查询 AE TEV UHFPRPS HFCTPRPS 通过m_storageengine查询
    if(inStorage.contains(eDBType))
    {
        QVector<common::DataWrapper> tmpdatas;
        auto status = m_storageengine->getRecordInTimePeriod(strPointID, timeBegin, timeEnd, &tmpdatas);
        if (!status.ok())
        {
            return bRet;
        }

        std::transform(tmpdatas.cbegin(), tmpdatas.cend(), std::back_inserter(autoIDList),
                       [](const common::DataWrapper& itrValue){
            return static_cast<qint64>(itrValue.getData()->dataIndex);
        });
        std::transform(tmpdatas.cbegin(), tmpdatas.cend(), std::back_inserter(recordTimeList),
                       [](const common::DataWrapper& itrValue){
            return itrValue.getData()->getRecordTime();
        });
    }
    else
    {
        if(m_mapDBs.contains( eDBType ))
        {
            switch (eDBType)
            {
            case DB_TEMP:
            {
                QVector<TEMPRecord> tempVector = getTempratureScanData(strPointID, timeBegin, timeEnd);
                for(auto iter = tempVector.cbegin(); iter != tempVector.cend(); ++iter)
                {
                    autoIDList.append(iter->autoId);
                    recordTimeList.append(iter->recordTime);
                }
            }
                break;
            default:
                break;
            }

        }
        else
        {
            PDS_SYS_WARNING_LOG("DBServer::getAutoIDAndRecordTimeListForTimePeriod, invalid db type: %d", eDBType);
            return bRet;
        }
    }

    logInfo("DBServer::getAutoIDAndRecordTimeListForTimePeriod: ") << autoIDList.size() << recordTimeList.size() << timeBegin << timeEnd << strPointID;
    if(!autoIDList.isEmpty() && !recordTimeList.isEmpty())
    {
        bRet = true;
    }

    return bRet;
}


bool DBServer::getRecordListFromDateTime(const QString &strADUID, quint8 ucChannelID, const QDateTime &timeBegin, const QDateTime &timeEnd, DataRecordList *records)
{
    ADUChannelType eChannelType;
    m_configService->getChannelTypeFromID( strADUID , ucChannelID, eChannelType);

    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        bool bRet = false;
        QList<PointArchiveInfo> listPointArchiveInfo;

        if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
        {
            bRet = pdb->getRecordListFromDateTime( listPointArchiveInfo.first().strPointGUID, timeBegin, timeEnd, records);
        };

        return bRet;
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::getRecordIDListFromDateTime, invalid db type: %d", eDB);
        return false;
    }
}

/*************************************************
功能： 获取在在timeBegin和timeEnd时间范围内，避雷器数据库中RecordID的列表，用于比较数据是否已入库
输入参数：
        strADUID：前端ID
        ucChannelID: 通道ID
        timeBegin：起始日期
        timeEnd：停止日期
输出参数：
        listRecordID -- RecordID的列表
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::getArresterRecordIDList(const QString &strADUID, quint8 ucChannelID, const QDateTime &timeBegin, const QDateTime &timeEnd, QList<quint16> &listRecordID)
{
    QString strChannelName = getChannelName(strADUID, ucChannelID );

    ArresterDB* pdb = (ArresterDB*)m_mapDBs[ DB_ARRESTER ];

    return pdb->getRecordIDListFromDateTime( strChannelName, timeBegin, timeEnd, listRecordID);
}

/*************************************************
功能： 获取数据ID对应的数据记录
输入参数：
        num -- 记录id
返回值：
        数据记录
*************************************************************/
AERecord DBServer::getAERecord(quint64 num)
{
    PDS_SYS_INFO_LOG("DBServer::getAERecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getAERecord end");});

    common::DataWrapper wrapdata{common::PdAeData()};
    Status status = Status::OK();
    if (!(status = m_storageengine->getRecordByGlobalIndex(num, &wrapdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }

    AERecord rtn;
    auto unwrapData = wrapdata.getData<common::PdAeData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdAeData(unwrapData->pointInfo.pointId, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}
TEVRecord DBServer::getTEVRecord(quint64 num)
{
    PDS_SYS_INFO_LOG("DBServer::getTEVRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getTEVRecord end");});

    common::DataWrapper wrapdata{common::PdTevData()};
    Status status = Status::OK();
    if (!(status = m_storageengine->getRecordByGlobalIndex(num, &wrapdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }

    TEVRecord rtn;
    auto unwrapData = wrapdata.getData<common::PdTevData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdTevData(unwrapData->pointInfo.pointId, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}
PRPSRecord DBServer::getUHFRecord(quint64 num)
{
    PDS_SYS_INFO_LOG("DBServer::getUHFRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getUHFRecord end");});

    common::DataWrapper wrapdata{common::PdUhfData()};
    Status status = Status::OK();
    if (!(status = m_storageengine->getRecordByGlobalIndex(num, &wrapdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }

    PRPSRecord rtn;
    auto unwrapData = wrapdata.getData<common::PdUhfData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdUhfData(unwrapData->pointInfo.pointId, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }

    return rtn;
}
PRPSRecord DBServer::getHFCTRecord(quint64 num)
{
    PDS_SYS_INFO_LOG("DBServer::getHFCTRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getHFCTRecord end");});

    common::DataWrapper wrapdata{common::PdHfctData()};
    Status status = Status::OK();
    if (!(status = m_storageengine->getRecordByGlobalIndex(num, &wrapdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }

    PRPSRecord rtn;
    auto unwrapData = wrapdata.getData<common::PdHfctData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdHfctData(unwrapData->pointInfo.pointId, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }

    return rtn;
}
MechDefine::MechISRecord DBServer::getMECHRecord(quint64 num)
{
    MechDB* pdb = (MechDB*)m_mapDBs[DB_MECH];

    DataRecord dbRecord = pdb->getRecord( num );
    MechDefine::MechISRecord record;
    record = getMECHRecord(dbRecord);

    return record;
}
TEMPRecord DBServer::getTEMPRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_TEMP];

    DataRecord dbRecord = pdb->getRecord( num );
    TEMPRecord record;
    record = getTEMPRecord(dbRecord);

    return record;
}
HumidityRecord DBServer::getHumidityRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_HUMI];

    DataRecord dbRecord = pdb->getRecord( num );
    HumidityRecord record;
    record = getHumidityRecord(dbRecord);

    return record;
}

FrostRawRecord DBServer::getFrostRawRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_FROST_RAW];

    DataRecord dbRecord = pdb->getRecord( num );
    FrostRawRecord record;
    record = getFrostRawRecord(dbRecord);

    return record;
}
FrostAtmRecord DBServer::getFrostAtmRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_FROST_ATM];

    DataRecord dbRecord = pdb->getRecord( num );
    FrostAtmRecord record;
    record = getFrostAtmRecord(dbRecord);

    return record;
}
DewRawRecord DBServer::getDewRawRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_DEW_RAW];

    DataRecord dbRecord = pdb->getRecord( num );
    DewRawRecord record;
    record = getDewRawRecord(dbRecord);

    return record;
}
DewAtmRecord DBServer::getDewAtmRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_DEW_ATM];

    DataRecord dbRecord = pdb->getRecord( num );
    DewAtmRecord record;
    record = getDewAtmRecord(dbRecord);

    return record;
}
MoistureRecord DBServer::getMoistureRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_MOISTURE];

    DataRecord dbRecord = pdb->getRecord( num );
    MoistureRecord record;
    record = getMoistureRecord(dbRecord);

    return record;
}
PressAbsoRecord DBServer::getPressAbsoRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_PRESS_ABSO];

    DataRecord dbRecord = pdb->getRecord( num );
    PressAbsoRecord record;
    record = getPressAbsoRecord(dbRecord);

    return record;
}
PressNormRecord DBServer::getPressNormRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_PRESS_NORM];

    DataRecord dbRecord = pdb->getRecord( num );
    PressNormRecord record;
    record = getPressNormRecord(dbRecord);

    return record;
}
DensityRecord DBServer::getDensityRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_DENSITY];

    DataRecord dbRecord = pdb->getRecord( num );
    DensityRecord record;
    record = getDensityRecord(dbRecord);

    return record;
}
OxygenRecord DBServer::getOxygenRecord(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_OXYGEN];

    DataRecord dbRecord = pdb->getRecord( num );
    OxygenRecord record;
    record = getOxygenRecord(dbRecord);

    return record;
}
SF6Record DBServer::getSF6Record(quint64 num)
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_SF6];

    DataRecord dbRecord = pdb->getRecord( num );
    SF6Record record;
    record = getSF6Record(dbRecord);

    return record;
}

ArresterRecord DBServer::getArresterRecord(quint64 num)
{
    ArresterRecord record;
    SqliteDB *pdb = dynamic_cast<SqliteDB*>(m_mapDBs[DB_ARRESTER]);
    if ( pdb )
    {
        DataRecord dbRecord = pdb->getRecord( num );
        record = getArresterRecord(dbRecord);
    }
    else
    {
        PDS_SYS_ERR_LOG("fail to getArresterRecord");
    }

    return record;
}

Sf6StateRecord DBServer::getSf6StateRecord(quint64 num) const
{
    Sf6StateRecord record;
    ENVDB *pdb = dynamic_cast<ENVDB*>(m_mapDBs[DB_SF6_STATE]);
    if ( pdb )
    {
        DataRecord dbRecord = pdb->getRecord( num );
        record = getSf6StateRecord(dbRecord);
    }
    else
    {
        PDS_SYS_ERR_LOG("fail to getSf6StateRecord");
    }

    return record;
}

EnvStatusRecord DBServer::getEnvStatusRecord(quint64 num) const
{
    EnvStatusRecord record;
    ENVDB *pdb = dynamic_cast<ENVDB*>(m_mapDBs[DB_ENV_STATUS]);
    if ( pdb )
    {
        DataRecord dbRecord = pdb->getRecord( num );
        record = getEnvStatusRecord(dbRecord);
    }
    else
    {
        PDS_SYS_ERR_LOG("fail to getEnvStatusRecord");
    }

    return record;
}

HkVideoRecord DBServer::getHkVideoRecord(quint64 num) const
{
    HkVideoRecord record;
    ENVDB *pdb = dynamic_cast<ENVDB*>(m_mapDBs[DB_HK_VIDEO]);
    if ( pdb )
    {
        DataRecord dbRecord = pdb->getRecord( num );
        record = getHkVideoRecord(dbRecord);
    }
    else
    {
        PDS_SYS_ERR_LOG("fail to getSmokeRecord");
    }

    return record;
}

/*************************************************
功能： 获取震动数据
输入参数：num 记录id
输出参数: vibRecord 震动数据
返回值：获取结果
*************************************************************/
bool DBServer::getVibrationRecord(quint64 num, VibrationRecord& vibRecord)
{
    bool result = true;
    SqliteDB *pdb = dynamic_cast<SqliteDB*>(m_mapDBs[DB_VIBRATION]);
    if ( pdb )
    {
        DataRecord dbRecord = pdb->getRecord(num);
        vibRecord = getVibrationRecord(dbRecord);
    }
    else
    {
        result = false;
        PDS_SYS_ERR_LOG("fail to getVibrationRecord: %ld", num);
    }
    return result;
}

/*************************************************
功能： 获取ae数据
输入参数：
    stChannelOutLineInfo -- 通道信息
    iAutoId -- autoid
返回值： ae数据
*************************************************************/
AERecord DBServer::getAERecord(const QString &strPointID, int iAutoId)
{
    PDS_SYS_INFO_LOG("DBServer::getAERecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getAERecord end");});

    common::DataWrapper wrapdata;
    Status status = Status::OK();
    if (!(status = m_storageengine->getRecordByIndex(strPointID, iAutoId, &wrapdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }

    AERecord rtn;
    auto unwrapData = wrapdata.getData<common::PdAeData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdAeData(unwrapData->pointInfo.pointId, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}
TEVRecord DBServer::getTEVRecord(const QString &strPointID, int iAutoId)
{
    PDS_SYS_INFO_LOG("DBServer::getTEVRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getTEVRecord end");});

    common::DataWrapper wrapdata;
    Status status = Status::OK();
    if (!(status = m_storageengine->getRecordByIndex(strPointID, iAutoId, &wrapdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    TEVRecord rtn;
    auto unwrapData = wrapdata.getData<common::PdTevData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdTevData(strPointID, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}
PRPSRecord DBServer::getUHFRecord(const QString &strPointID, int iAutoId)
{
    PDS_SYS_INFO_LOG("DBServer::getUHFRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getUHFRecord end");});

    common::DataWrapper wrapdata;
    Status status = Status::OK();
    if (!(status = m_storageengine->getRecordByIndex(strPointID, iAutoId, &wrapdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    PRPSRecord rtn;
    auto unwrapData = wrapdata.getData<common::PdUhfData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdUhfData(strPointID, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}

PRPSRecord DBServer::getTEVPRPSRecord(const QString &strPointID, int iAutoId)
{
    PDS_SYS_INFO_LOG("DBServer::getTEVPRPSRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getTEVPRPSRecord end");});

    common::DataWrapper wrapdata;
    Status status = Status::OK();
    if (!(status = m_storageengine->getRecordByIndex(strPointID, iAutoId, &wrapdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    PRPSRecord rtn;
    auto unwrapData = wrapdata.getData<common::PdTEVPRPSData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdTEVPRPSData(strPointID, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}

PRPSRecord DBServer::getHFCTRecord(const QString &strPointID, int iAutoId)
{
    PDS_SYS_INFO_LOG("DBServer::getHFCTRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getHFCTRecord end");});

    common::DataWrapper wrapdata;
    Status status = Status::OK();
    if (!(status = m_storageengine->getRecordByIndex(strPointID, iAutoId, &wrapdata)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    PRPSRecord rtn;
    auto unwrapData = wrapdata.getData<common::PdHfctData>();
    if (!unwrapData)
    {
        PDS_SYS_ERR_LOG("unwrap data failed");
    }
    else
    {
        if (!(status = DataTypeConvertionUtils::convertFromPdHfctData(strPointID, *unwrapData, &rtn)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        }
    }
    return rtn;
}

/*************************************************
功能： 获取机械特性数据
输入参数：
    strPointID -- 测点ID
    iAutoId -- autoid
返回值： 机械特性数据
*************************************************************/
MechDefine::MechISRecord DBServer::getMECHRecord(const QString &strPointID, int iAutoId)
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_MECH];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    MechDefine::MechISRecord record;
    record = getMECHRecord(dbRecord);

    return record;
}

/*************************************************
功能： 获取温度数据
输入参数：
    strPointID -- 测点ID
    iAutoId -- autoid
返回值： 温度数据
*************************************************************/
TEMPRecord DBServer::getTEMPRecord(const QString &strPointID, int iAutoId)
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_TEMP];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    TEMPRecord record;
    record = getTEMPRecord(dbRecord);

    return record;
}

/*************************************************
功能： 获取湿度数据
输入参数：
    strPointID -- 测点ID
    iAutoId -- autoid
返回值： 湿度数据
*************************************************************/
HumidityRecord DBServer::getHumidityRecord(const QString &strPointID, int iAutoId)
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_HUMI];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    HumidityRecord record;
    record = getHumidityRecord(dbRecord);

    return record;
}

ArresterRecord DBServer::getArresterRecord(const QString &strPointID, int iAutoId)
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_ARRESTER];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    ArresterRecord record;
    record = getArresterRecord(dbRecord);

    return record;
}

ArresterRecord DBServer::getArresterRecord(const QString &strPointID, const QDateTime &dateTime)
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_ARRESTER];
    DataRecord dbRecord = pdb->getRecord( strPointID, dateTime );

    ArresterRecord record;
    record = getArresterRecord(dbRecord);

    return record;
}

ArresterRecord DBServer::getArresterRecord(const QString &strPointID, ChannelPhase eChannelPhase, const QDateTime &dateTime)
{
    ArresterDB* pdb = (ArresterDB*)m_mapDBs[DB_ARRESTER];
    DataRecord dbRecord;
    pdb->getRecord( strPointID, eChannelPhase, dateTime, dbRecord );

    ArresterRecord record;
    record = getArresterRecord(dbRecord);

    return record;
}

QVector<ArresterRecord> DBServer::getArresterScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    return transformSequence<QVector<ArresterRecord> >(getDataByScanTime(DB_ARRESTER, pointId, start, end),\
                                                    [this](const DataRecord& record){return getArresterRecord(record);});
}

VibrationRecord DBServer::getVibrationRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_VIBRATION];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    VibrationRecord record;
    record = getVibrationRecord(dbRecord);

    return record;
}

VibrationRecord DBServer::getVibrationRecord( const QString &strPointID,  const QDateTime &dateTime )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_VIBRATION];
    DataRecord dbRecord = pdb->getRecord( strPointID, dateTime );

    VibrationRecord record;
    record = getVibrationRecord(dbRecord);

    return record;
}

FrostRawRecord DBServer::getFrostRawRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_FROST_RAW];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    FrostRawRecord record;
    record = getFrostRawRecord(dbRecord);

    return record;
}
FrostAtmRecord DBServer::getFrostAtmRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_FROST_ATM];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    FrostAtmRecord record;
    record = getFrostAtmRecord(dbRecord);

    return record;
}
DewRawRecord DBServer::getDewRawRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_DEW_RAW];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    DewRawRecord record;
    record = getDewRawRecord(dbRecord);

    return record;
}
DewAtmRecord DBServer::getDewAtmRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_DEW_ATM];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    DewAtmRecord record;
    record = getDewAtmRecord(dbRecord);

    return record;
}
MoistureRecord DBServer::getMoistureRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_MOISTURE];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    MoistureRecord record;
    record = getMoistureRecord(dbRecord);

    return record;
}
PressAbsoRecord DBServer::getPressAbsoRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_PRESS_ABSO];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    PressAbsoRecord record;
    record = getPressAbsoRecord(dbRecord);

    return record;
}
PressNormRecord DBServer::getPressNormRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_PRESS_NORM];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    PressNormRecord record;
    record = getPressNormRecord(dbRecord);

    return record;
}
DensityRecord DBServer::getDensityRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_DENSITY];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    DensityRecord record;
    record = getDensityRecord(dbRecord);

    return record;
}
OxygenRecord DBServer::getOxygenRecord( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_OXYGEN];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    OxygenRecord record;
    record = getOxygenRecord(dbRecord);

    return record;
}


SF6Record DBServer::getSF6Record( const QString &strPointID, int iAutoId )
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_SF6];
    DataRecord dbRecord = pdb->getRecord( strPointID, iAutoId );

    SF6Record record;
    record = getSF6Record(dbRecord);

    return record;
}

Sf6StateRecord DBServer::getSf6StateRecord(const QString &pointId, int autoId) const
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_SF6_STATE];
    DataRecord dbRecord = pdb->getRecord(pointId, autoId);
    return getSf6StateRecord(dbRecord);
}

EnvStatusRecord DBServer::getEnvStatusRecord(const QString &pointId, int autoId) const
{
    auto pdb = reinterpret_cast<SqliteDB*>(m_mapDBs[DB_ENV_STATUS]);
    DataRecord dbRecord = pdb->getRecord(pointId, autoId);
    return getEnvStatusRecord(dbRecord);
}

HkVideoRecord DBServer::getHkVideoRecord(const QString &pointId, int autoId) const
{
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_HK_VIDEO];
    DataRecord dbRecord = pdb->getRecord(pointId, autoId);
    return getHkVideoRecord(dbRecord);
}

/*************************************************
功能： 向数据库中添加一条机械特性记录数据
输入参数：
        record：数据记录
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::addMechRecord( const MechDefine::MechRecord &record)
{
    PDS_SYS_INFO_LOG("addMechRecord");
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_MECH];
    DataRecord dbRecord;
    QByteArray byteArray;

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName( record.ucAddr, record.ucChannel ));
    dbRecord.setData( IK_RECORDTIME, QDateTime( QDate((int)record.stMechRecordInfo.ucYear+2000, (int)record.stMechRecordInfo.ucMonth, (int)record.stMechRecordInfo.ucDay) ,
                                                  QTime((int)record.stMechRecordInfo.ucHour,(int)record.stMechRecordInfo.ucMinute,(int)record.stMechRecordInfo.ucSecond)).toString(DATETIME_FORMAT));
    dbRecord.setData( IK_RECORDTIME_T, QDateTime( QDate((int)record.stMechRecordInfo.ucYear+2000, (int)record.stMechRecordInfo.ucMonth, (int)record.stMechRecordInfo.ucDay) ,
                                                  QTime((int)record.stMechRecordInfo.ucHour,(int)record.stMechRecordInfo.ucMinute,(int)record.stMechRecordInfo.ucSecond)).toTime_t());
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.stMechRecordInfo.usID );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );
    dbRecord.setData( SAMPLE_DATA_DATA_ID, m_configService->getGUID() );

    dbRecord.setData( MECH_DATA_TYPE, 0 );
    dbRecord.setData( MECH_FUNCTION_TYPE, 0 );
    dbRecord.setData( MECH_STROAGE_TYPE, 1 );

    dbRecord.setData( MECH_LOOP_THRESHOLD, record.dLoopThreshold );

    if (record.chanACurrentLst.count() > 0)
    {
        byteArray.resize(record.chanACurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.chanACurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_CHANNEL_A, byteArray );
    byteArray.clear();

    if (record.chanBCurrentLst.count() > 0)
    {
        byteArray.resize(record.chanBCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.chanBCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_CHANNEL_B, byteArray );
    byteArray.clear();

    if (record.chanCCurrentLst.count() > 0)
    {
        byteArray.resize(record.chanCCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.chanCCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_CHANNEL_C, byteArray );
    byteArray.clear();

    if (record.loopCurrentLst.count() > 0)
    {
        byteArray.resize(record.loopCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.loopCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_JOIN_A_LOOP, byteArray );
    byteArray.clear();

    dbRecord.setData( MECH_THRESHOLD, record.dMechThreshold );

    if (record.mechCurrentLst.count() > 0)
    {
        byteArray.resize(record.mechCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.mechCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CHAN_A_CURRENT, byteArray );
    byteArray.clear();

    dbRecord.setData( MECH_SWITCH_STATE, record.eSwitchState );
    dbRecord.setData( MECH_DIVIDE_PERIOD, record.dDividePeriod );
    dbRecord.setData( MECH_JOIN_PERIOD, record.dJoinPeriod );
    dbRecord.setData( MECH_DIVIDE_PERIOD_A, record.dADividePeriod );
    dbRecord.setData( MECH_JOIN_PERIOD_A, record.dAJoinPeriod );
    dbRecord.setData( MECH_DIVIDE_PERIOD_B, record.dBDividePeriod );
    dbRecord.setData( MECH_JOIN_PERIOD_B, record.dBJoinPeriod );
    dbRecord.setData( MECH_DIVIDE_PERIOD_C, record.dCDividePeriod );
    dbRecord.setData( MECH_JOIN_PERIOD_C, record.dCJoinPeriod );
    dbRecord.setData( MECH_DIVIDE_SYNC_PERIOD, record.dDivideSyncPeriod );
    dbRecord.setData( MECH_JOIN_SYNC_PERIOD, record.dJoinSyncPeriod );

    if (record.aSwitchStateLst.count() > 0)
    {
        byteArray.resize(record.aSwitchStateLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.aSwitchStateLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_SWITCHSTATE_A, byteArray );
    byteArray.clear();

    if (record.bSwitchStateLst.count() > 0)
    {
        byteArray.resize(record.bSwitchStateLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.bSwitchStateLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_SWITCHSTATE_B, byteArray );
    byteArray.clear();

    if (record.cSwitchStateLst.count() > 0)
    {
        byteArray.resize(record.cSwitchStateLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.cSwitchStateLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_SWITCHSTATE_C, byteArray );
    byteArray.clear();

    dbRecord.setData( MECH_JOIN_A_LOOP_UP_TIME, record.dLoopUpTime );
    dbRecord.setData( MECH_JOIN_A_HIT_TIME, record.dHitTime );
    dbRecord.setData( MECH_JOIN_A_SUBSWITCH_CLOSE_TIME, record.dSubSwitchCloseTime );
    dbRecord.setData( MECH_JOIN_A_LOOP_DOWN_TIME, record.dLoopDownTime );
    dbRecord.setData( MECH_JOIN_A_LOOP_CURRENT, record.dMaxLoopCurrent );
    dbRecord.setData( MECH_CHAN_A_START_CURRENT, record.dMechStartCurrent );
    dbRecord.setData( MECH_CHAN_A_MAX_CURRENT, record.dMaxMechCurrent );
    dbRecord.setData( MECH_CHAN_A_STOREAGE_PERIOD, record.dMechStoragePeriod );

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, QString::number(record.ucAddr), record.ucChannel);
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);

        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
//        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        dbRecord.setData( IK_GLOBAL_ID,pdb->updateGlobalID(pdb->getGlobalID(MECH_GLOBAL_ID_START)) );

        if (!pdb->addRecord(dbRecord))
        {
            PDS_SYS_ERR_LOG("add MechRecord failed, pointId: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());
        }
    }

    PDS_SYS_INFO_LOG("add MechRecord failed, finished: %s", dbRecord.value(SAMPLE_DATA_POINT_GUID).toString().toLatin1().data());

    return true;
}
QByteArray DBServer::makeVectorDoubleToByteArray(const QVector<double> &vecData)
{
    QByteArray byteArray;
    if (vecData.count() > 0)
    {
        byteArray.resize(vecData.size() * sizeof(double));
        memcpy(byteArray.data(), &vecData[0], byteArray.size());
    }
    return byteArray;
}

QByteArray DBServer::makeVectorUINT8ToByteArray(const QVector<quint8> &vecData)
{
    QByteArray byteArray;
    //byteArray.resize(vecData.size() * sizeof(vecData));
    if (vecData.count() > 0)
    {
        byteArray.resize(vecData.size() * sizeof(quint8));
        memcpy(byteArray.data(), &vecData[0], byteArray.size());
    }
    return byteArray;
}

/*************************************************
功能： 向数据库中添加一条机械特性记录数据
输入参数：
        record：数据记录
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::addMechRecordSync(const MechDefine::MechISRecord &record, std::function<void(QVector<PDSMPDATA> &)> fDataReport)
{
    logInfo(QString("add Mech Data, aduID:(%1), channelID:(%4), recordID:(%2), recordTime:(%3)")
            .arg(record.aduId)
            .arg(record.recordId)
            .arg(record.recordTime.toString(DATETIME_FORMAT))
            .arg(record.channelId));

    logTrace(QString("addMechRecordSync currentThread: ")) << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    //先查重,防止重复入库
    if(hasMechRecord(record.aduId, record.channelId, record.recordId, record.recordTime, record.recordTime))
    {
        logWarnning(QString("The Mech data already exists in the database, aduID:(%1), recordID:(%2), recordTime:(%3).")
                    .arg(record.aduId)
                    .arg(record.recordId)
                    .arg(record.recordTime.toString(DATETIME_FORMAT)));
        return false;
    }

    SqliteDB* pdb = m_mapDBs[DB_MECH];

    if(!pdb)
    {
        return false;
    }

    DataRecord dbRecord;
    QByteArray byteArray;

    dbRecord.setData( SAMPLE_DATA_CHANNEL_NAME, getChannelName( record.aduId, record.channelId ));
    dbRecord.setData( IK_RECORDTIME, record.recordTime.toString(DATETIME_FORMAT));
    dbRecord.setData( IK_RECORDTIME_T, record.recordTime.toTime_t());
    dbRecord.setData( SAMPLE_DATA_RECORD_ID, record.recordId );

    dbRecord.setData( SAMPLE_DATA_IS_DATA_UPDATE, 0 );

    if (!record.loopTiggerTime.isValid())
    {
        dbRecord.setData( MECH_DATA_TYPE, 2 );
    }
    else if (!record.mechTiggerTime.isValid())
    {
        dbRecord.setData( MECH_DATA_TYPE, 3 );
    }
    else
    {
        dbRecord.setData( MECH_DATA_TYPE, 1 );
    }

    dbRecord.setData( MECH_FUNCTION_TYPE, record.iMechFunctionType );
    dbRecord.setData( MECH_STROAGE_TYPE, record.iMechStorageType );

    dbRecord.setData( MECH_LOOP_THRESHOLD, record.dLoopThreshold );
    dbRecord.setData( MECH_LOOP_SAMPLE_RATE, 10000 );
    dbRecord.setData( MECH_LOOP_TIGGER_TIME, record.loopTiggerTime.toString(DATETIME_FORMAT));

    byteArray.resize(record.loopCloseACurrentLst.size() * sizeof(double));
    if (record.chanACurrentLst.count() > 0)
    {
        byteArray.resize(record.chanACurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.chanACurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_CHANNEL_A, byteArray );
    byteArray.clear();

    if (record.chanBCurrentLst.count() > 0)
    {
        byteArray.resize(record.chanBCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.chanBCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_CHANNEL_B, byteArray );
    byteArray.clear();

    if (record.chanCCurrentLst.count() > 0)
    {
        byteArray.resize(record.chanCCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.chanCCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_CHANNEL_C, byteArray );
    byteArray.clear();

    if (record.loopCloseACurrentLst.count() > 0)
    {
        byteArray.resize(record.loopCloseACurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.loopCloseACurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_JOIN_A_LOOP, byteArray );
    byteArray.clear();

    if (record.loopCloseBCurrentLst.count() > 0)
    {
        byteArray.resize(record.loopCloseBCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.loopCloseBCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_JOIN_B_LOOP, byteArray );
    byteArray.clear();

    if (record.loopCloseCCurrentLst.count() > 0)
    {
        byteArray.resize(record.loopCloseCCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.loopCloseCCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_JOIN_C_LOOP, byteArray );
    byteArray.clear();

    if (record.loopOpenACurrentLst.count() > 0)
    {
        byteArray.resize(record.loopOpenACurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.loopOpenACurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_DEVICE_A_LOOP, byteArray );
    byteArray.clear();

    if (record.loopOpenBCurrentLst.count() > 0)
    {
        byteArray.resize(record.loopOpenBCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.loopOpenBCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_DEVICE_B_LOOP, byteArray );
    byteArray.clear();

    if (record.loopOpenCCurrentLst.count() > 0)
    {
        byteArray.resize(record.loopOpenCCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.loopOpenCCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CURRENT_DEVICE_C_LOOP, byteArray );
    byteArray.clear();

    dbRecord.setData( MECH_THRESHOLD, record.dMechThreshold );
    dbRecord.setData( MECH_MECH_SAMPLE_RATE, 100 );
    dbRecord.setData( MECH_MECH_TIGGER_TIME, record.mechTiggerTime.toString(DATETIME_FORMAT));

    if (record.mechChanACurrentLst.count() > 0)
    {
        byteArray.resize(record.mechChanACurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.mechChanACurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CHAN_A_CURRENT, byteArray );
    byteArray.clear();

    if (record.mechChanBCurrentLst.count() > 0)
    {
        byteArray.resize(record.mechChanBCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.mechChanBCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CHAN_B_CURRENT, byteArray );
    byteArray.clear();

    if (record.mechChanCCurrentLst.count() > 0)
    {
        byteArray.resize(record.mechChanCCurrentLst.size() * sizeof(double));
        memcpy(byteArray.data(), &record.mechChanCCurrentLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_CHAN_C_CURRENT, byteArray );
    byteArray.clear();

    dbRecord.setData( MECH_SWITCH_STATE, record.eSwitchState );
    dbRecord.setData( MECH_DIVIDE_PERIOD, record.dDividePeriod );
    dbRecord.setData( MECH_JOIN_PERIOD, record.dJoinPeriod );
    dbRecord.setData( MECH_DIVIDE_PERIOD_A, record.dADividePeriod );
    dbRecord.setData( MECH_JOIN_PERIOD_A, record.dAJoinPeriod );
    dbRecord.setData( MECH_DIVIDE_PERIOD_B, record.dBDividePeriod );
    dbRecord.setData( MECH_JOIN_PERIOD_B, record.dBJoinPeriod );
    dbRecord.setData( MECH_DIVIDE_PERIOD_C, record.dCDividePeriod );
    dbRecord.setData( MECH_JOIN_PERIOD_C, record.dCJoinPeriod );
    dbRecord.setData( MECH_DIVIDE_SYNC_PERIOD, record.dDivideSyncPeriod );
    dbRecord.setData( MECH_JOIN_SYNC_PERIOD, record.dJoinSyncPeriod );

    dbRecord.setData( MECH_TWICE_DIVIDE_PERIOD, record.dTwiceDivideTime );
    dbRecord.setData( MECH_TWICE_DIVIDE_SYNC_PERIOD, record.dTwiceDivideSyncPeriod );
    dbRecord.setData( MECH_TWICE_DIVIDE_PERIOD_A, record.dTwiceADividePeriod );
    dbRecord.setData( MECH_TWICE_DIVIDE_PERIOD_B, record.dTwiceBDividePeriod );
    dbRecord.setData( MECH_TWICE_DIVIDE_PERIOD_C, record.dTwiceCDividePeriod );

    dbRecord.setData( MECH_SWITCH_SHOT_TIME_A, record.dASwitchShotTime );
    dbRecord.setData( MECH_SWITCH_NO_CURRENT_TIME_A, record.dASwitchNoCurrentTime );
    dbRecord.setData( MECH_SWITCH_SHOT_TIME_B, record.dBSwitchShotTime );
    dbRecord.setData( MECH_SWITCH_NO_CURRENT_TIME_B, record.dBSwitchNoCurrentTime );
    dbRecord.setData( MECH_SWITCH_SHOT_TIME_C, record.dCSwitchShotTime );
    dbRecord.setData( MECH_SWITCH_NO_CURRENT_TIME_C, record.dCSwitchNoCurrentTime );

    if (record.aSwitchStateLst.count() > 0)
    {
        byteArray.resize(record.aSwitchStateLst.size());
        memcpy(byteArray.data(), &record.aSwitchStateLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_SWITCHSTATE_A, byteArray );
    byteArray.clear();

    if (record.bSwitchStateLst.count() > 0)
    {
        byteArray.resize(record.bSwitchStateLst.size());
        memcpy(byteArray.data(), &record.bSwitchStateLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_SWITCHSTATE_B, byteArray );
    byteArray.clear();

    if (record.cSwitchStateLst.count() > 0)
    {
        byteArray.resize(record.cSwitchStateLst.size());
        memcpy(byteArray.data(), &record.cSwitchStateLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_SWITCHSTATE_C, byteArray );
    byteArray.clear();

    if (record.SwitchStateLst.count() > 0)
    {
        byteArray.resize(record.SwitchStateLst.size());
        memcpy(byteArray.data(), &record.SwitchStateLst[0], byteArray.size());
    }
    dbRecord.setData( MECH_SWITCH_STATE_LIST, byteArray );
    byteArray.clear();

    dbRecord.setData( MECH_JOIN_A_LOOP_UP_TIME, record.dCloseALoopUpTime );
    dbRecord.setData( MECH_JOIN_A_HIT_TIME, record.dCloseAHitTime );
    dbRecord.setData( MECH_JOIN_A_SUBSWITCH_CLOSE_TIME, record.dCloseASubSwitchCloseTime );
    dbRecord.setData( MECH_JOIN_A_LOOP_DOWN_TIME, record.dCloseALoopDownTime );
    dbRecord.setData( MECH_JOIN_A_LOOP_CURRENT, record.dCloseAMaxLoopCurrent );

    dbRecord.setData( MECH_JOIN_B_LOOP_UP_TIME, record.dCloseBLoopUpTime );
    dbRecord.setData( MECH_JOIN_B_HIT_TIME, record.dCloseBHitTime );
    dbRecord.setData( MECH_JOIN_B_SUBSWITCH_CLOSE_TIME, record.dCloseBSubSwitchCloseTime );
    dbRecord.setData( MECH_JOIN_B_LOOP_DOWN_TIME, record.dCloseBLoopDownTime );
    dbRecord.setData( MECH_JOIN_B_LOOP_CURRENT, record.dCloseBMaxLoopCurrent );

    dbRecord.setData( MECH_JOIN_C_LOOP_UP_TIME, record.dCloseCLoopUpTime );
    dbRecord.setData( MECH_JOIN_C_HIT_TIME, record.dCloseCHitTime );
    dbRecord.setData( MECH_JOIN_C_SUBSWITCH_CLOSE_TIME, record.dCloseCSubSwitchCloseTime );
    dbRecord.setData( MECH_JOIN_C_LOOP_DOWN_TIME, record.dCloseCLoopDownTime );
    dbRecord.setData( MECH_JOIN_C_LOOP_CURRENT, record.dCloseCMaxLoopCurrent );

    dbRecord.setData( MECH_DEVICE_A_LOOP_UP_TIME, record.dOpenALoopUpTime );
    dbRecord.setData( MECH_DEVICE_A_HIT_TIME, record.dOpenAHitTime );
    dbRecord.setData( MECH_DEVICE_A_SUBSWITCH_CLOSE_TIME, record.dOpenASubSwitchCloseTime );
    dbRecord.setData( MECH_DEVICE_A_LOOP_DOWN_TIME, record.dOpenALoopDownTime );
    dbRecord.setData( MECH_DEVICE_A_LOOP_CURRENT, record.dOpenAMaxLoopCurrent );

    dbRecord.setData( MECH_DEVICE_B_LOOP_UP_TIME, record.dOpenBLoopUpTime );
    dbRecord.setData( MECH_DEVICE_B_HIT_TIME, record.dOpenBHitTime );
    dbRecord.setData( MECH_DEVICE_B_SUBSWITCH_CLOSE_TIME, record.dOpenBSubSwitchCloseTime );
    dbRecord.setData( MECH_DEVICE_B_LOOP_DOWN_TIME, record.dOpenBLoopDownTime );
    dbRecord.setData( MECH_DEVICE_B_LOOP_CURRENT, record.dOpenBMaxLoopCurrent );

    dbRecord.setData( MECH_DEVICE_C_LOOP_UP_TIME, record.dOpenCLoopUpTime );
    dbRecord.setData( MECH_DEVICE_C_HIT_TIME, record.dOpenCHitTime );
    dbRecord.setData( MECH_DEVICE_C_SUBSWITCH_CLOSE_TIME, record.dOpenCSubSwitchCloseTime );
    dbRecord.setData( MECH_DEVICE_C_LOOP_DOWN_TIME, record.dOpenCLoopDownTime );
    dbRecord.setData( MECH_DEVICE_C_LOOP_CURRENT, record.dOpenCMaxLoopCurrent );

    dbRecord.setData( MECH_TWICE_DEVICE_A_LOOP_UP_TIME, record.dTwiceOpenALoopUpTime );
    dbRecord.setData( MECH_TWICE_DEVICE_A_HIT_TIME, record.dTwiceOpenAHitTime );
    dbRecord.setData( MECH_TWICE_DEVICE_A_SUBSWITCH_CLOSE_TIME, record.dTwiceOpenASubSwitchCloseTime );
    dbRecord.setData( MECH_TWICE_DEVICE_A_LOOP_DOWN_TIME, record.dTwiceOpenALoopDownTime );
    dbRecord.setData( MECH_TWICE_DEVICE_A_LOOP_CURRENT, record.dTwiceOpenAMaxLoopCurrent );

    dbRecord.setData( MECH_TWICE_DEVICE_B_LOOP_UP_TIME, record.dTwiceOpenBLoopUpTime );
    dbRecord.setData( MECH_TWICE_DEVICE_B_HIT_TIME, record.dTwiceOpenBHitTime );
    dbRecord.setData( MECH_TWICE_DEVICE_B_SUBSWITCH_CLOSE_TIME, record.dTwiceOpenBSubSwitchCloseTime );
    dbRecord.setData( MECH_TWICE_DEVICE_B_LOOP_DOWN_TIME, record.dTwiceOpenBLoopDownTime );
    dbRecord.setData( MECH_TWICE_DEVICE_B_LOOP_CURRENT, record.dTwiceOpenBMaxLoopCurrent );

    dbRecord.setData( MECH_TWICE_DEVICE_C_LOOP_UP_TIME, record.dTwiceOpenCLoopUpTime );
    dbRecord.setData( MECH_TWICE_DEVICE_C_HIT_TIME, record.dTwiceOpenCHitTime );
    dbRecord.setData( MECH_TWICE_DEVICE_C_SUBSWITCH_CLOSE_TIME, record.dTwiceOpenCSubSwitchCloseTime );
    dbRecord.setData( MECH_TWICE_DEVICE_C_LOOP_DOWN_TIME, record.dTwiceOpenCLoopDownTime );
    dbRecord.setData( MECH_TWICE_DEVICE_C_LOOP_CURRENT, record.dTwiceOpenCMaxLoopCurrent );

    dbRecord.setData( MECH_CHAN_A_START_CURRENT, record.dAMechStartCurrent );
    dbRecord.setData( MECH_CHAN_A_MAX_CURRENT, record.dAMaxMechCurrent );
    dbRecord.setData( MECH_CHAN_A_STOREAGE_PERIOD, record.dAMechStoragePeriod );

    dbRecord.setData( MECH_CHAN_B_START_CURRENT, record.dBMechStartCurrent );
    dbRecord.setData( MECH_CHAN_B_MAX_CURRENT, record.dBMaxMechCurrent );
    dbRecord.setData( MECH_CHAN_B_STOREAGE_PERIOD, record.dBMechStoragePeriod );

    dbRecord.setData( MECH_CHAN_C_START_CURRENT, record.dCMechStartCurrent );
    dbRecord.setData( MECH_CHAN_C_MAX_CURRENT, record.dCMaxMechCurrent );
    dbRecord.setData( MECH_CHAN_C_STOREAGE_PERIOD, record.dCMechStoragePeriod );

    QList<PointArchiveInfo> listPointArchiveInfo;
    m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, record.aduId, record.channelId);

    QVector<PDSMPDATA> reportDataVector; //上送数据
    for (int i = 0; i < listPointArchiveInfo.size(); i++)
    {
        const PointArchiveInfo &stPointArchiveInfo = listPointArchiveInfo.at(i);
        dbRecord.setData( SAMPLE_DATA_DATA_ID, record.recordStringId  + QString::number(i));
        dbRecord.setData( SAMPLE_DATA_STATION_PMS, stPointArchiveInfo.strStationPMS );
        dbRecord.setData( SAMPLE_DATA_DEVICE_PMS, stPointArchiveInfo.strDevicePMS );
        dbRecord.setData( SAMPLE_DATA_STATION_ID, stPointArchiveInfo.strStationGUID );
        dbRecord.setData( SAMPLE_DATA_DEVICE_ID, stPointArchiveInfo.strDeviceGUID );
        dbRecord.setData( SAMPLE_DATA_POINT_GUID, stPointArchiveInfo.strPointGUID );
//        dbRecord.setData( SAMPLE_DATA_POINT_NAME, stPointArchiveInfo.strPointName );

        uint64_t globalId = pdb->updateGlobalID(pdb->getGlobalID(MECH_GLOBAL_ID_START));
        dbRecord.setData(IK_GLOBAL_ID, globalId);

        int64_t currentId = -1;

        if (!pdb->addRecord(dbRecord, currentId))
        {
            PDS_SYS_ERR_LOG("add MechRecord failed, pointId: %s", stPointArchiveInfo.strPointGUID.toLatin1().data());
            return false;
        }

        if (fDataReport)
        {
            MechDefine::MechISRecord mechRecord(record);
            mechRecord.autoId = currentId;
            mechRecord.globalId = globalId;
            mechRecord.pointArchiveInfo = stPointArchiveInfo;
            mechRecord.recordStringId = record.recordStringId+QString::number(i);

            PDSMPDATA reportData;
            reportData.data.setValue(mechRecord);
            reportDataVector.append(reportData);
        }
    }

    logInfo("add Mech data true.");

    if(fDataReport && !reportDataVector.isEmpty())
    {
        logInfo("Mech data report...");
        fDataReport(reportDataVector);
    }

    return true;
}

bool DBServer::addMechRecordAsync(const MechDefine::MechISRecord &record, std::function<void (QVector<PDSMPDATA> &)> fDataReport)
{
    logTrace("addMechRecordAsync currentThread: ") << QThread::currentThread();
    logTrace("threadpool status: ") << m_serverThreadPool.maxThreadCount() << m_serverThreadPool.activeThreadCount();

    QtConcurrent::run(&m_serverThreadPool, this, &DBServer::addMechRecordSync, record, fDataReport);

    return true;
}

/*************************************************
功能： 判断在timeBegin和timeEnd时间范围内，数据库中是否存在前端号为ucAddr、记录号为usMechRecordID的机械特性记录数据
输入参数：
        ucID：前端ID
        usMechRecordID：记录ID
        timeBegin：起始日期
        timeEnd：停止日期
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::hasMechRecord( quint8 ucID, quint8 ucChannelID, quint16 usMechRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    return hasMechRecord(QString::number(ucID), ucChannelID, usMechRecordID, timeBegin, timeEnd);
}
bool DBServer::hasMechRecord( const QString &staADUID, quint8 ucChannelID, quint16 usMechRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    MechDB* pdb = (MechDB*)m_mapDBs[DB_MECH];
    bool  bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, staADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usMechRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

/*************************************************
功能： 将数据记录转换为机械特性记录
输入参数：
        dbRecord -- 数据记录集
返回值：
        机械特性记录
*************************************************************/
MechDefine::MechISRecord DBServer::getMECHRecord( const DataRecord& record ) const
{
    MechDefine::MechISRecord stMechRecord;
    stMechRecord.autoId = record.value(IK_ID).toInt();
    stMechRecord.isUpdate = record.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    stMechRecord.pointArchiveInfo.strDeviceGUID = record.value(SAMPLE_DATA_DEVICE_ID).toString();
    stMechRecord.pointArchiveInfo.strDevicePMS = record.value(SAMPLE_DATA_DEVICE_PMS).toString();
    stMechRecord.pointArchiveInfo.strPointGUID = record.value(SAMPLE_DATA_POINT_GUID).toString();
    stMechRecord.pointArchiveInfo.strStationGUID = record.value(SAMPLE_DATA_STATION_ID).toString();
    stMechRecord.pointArchiveInfo.strStationPMS = record.value(SAMPLE_DATA_STATION_PMS).toString();
    stMechRecord.globalId = record.value(IK_GLOBAL_ID).toLongLong();

    stMechRecord.channelId = getChannelInfo(record.value(SAMPLE_DATA_CHANNEL_NAME).toString(),stMechRecord.aduId);
    stMechRecord.recordTime = record.value(IK_RECORDTIME).toDateTime();
    stMechRecord.recordId = record.value(SAMPLE_DATA_RECORD_ID).toInt();
    stMechRecord.recordStringId = record.value(SAMPLE_DATA_DATA_ID).toString();
    stMechRecord.isUpdate = record.value(SAMPLE_DATA_IS_DATA_UPDATE).toBool();
    stMechRecord.iMechDataType = record.value(MECH_DATA_TYPE).toInt();
    stMechRecord.iMechFunctionType = record.value(MECH_FUNCTION_TYPE).toInt();
    stMechRecord.iMechStorageType = record.value(MECH_STROAGE_TYPE).toInt();

    stMechRecord.dLoopThreshold = record.value(MECH_LOOP_THRESHOLD).toDouble();
    stMechRecord.iLoopSampleRate = record.value(MECH_LOOP_SAMPLE_RATE).toInt();
    stMechRecord.loopTiggerTime = record.value(MECH_LOOP_TIGGER_TIME).toDateTime();
    QByteArray byteArray;

    byteArray = record.value(MECH_CURRENT_CHANNEL_A).toByteArray();
    stMechRecord.chanACurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.chanACurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.chanACurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CURRENT_CHANNEL_B).toByteArray();
    stMechRecord.chanBCurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.chanBCurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.chanBCurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CURRENT_CHANNEL_C).toByteArray();
    stMechRecord.chanCCurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.chanCCurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.chanCCurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CURRENT_JOIN_A_LOOP).toByteArray();
    stMechRecord.loopCloseACurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.loopCloseACurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.loopCloseACurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CURRENT_JOIN_B_LOOP).toByteArray();
    stMechRecord.loopCloseBCurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.loopCloseBCurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.loopCloseBCurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CURRENT_JOIN_C_LOOP).toByteArray();
    stMechRecord.loopCloseCCurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.loopCloseCCurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.loopCloseCCurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CURRENT_DEVICE_A_LOOP).toByteArray();
    stMechRecord.loopOpenACurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.loopOpenACurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.loopOpenACurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CURRENT_DEVICE_B_LOOP).toByteArray();
    stMechRecord.loopOpenBCurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.loopOpenBCurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.loopOpenBCurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CURRENT_DEVICE_C_LOOP).toByteArray();
    stMechRecord.loopOpenCCurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.loopOpenCCurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.loopOpenCCurrentLst[0], byteArray.data(), byteArray.size());
    }

    stMechRecord.dMechThreshold = record.value(MECH_THRESHOLD).toDouble();
    stMechRecord.iMechSampleRate = record.value(MECH_MECH_SAMPLE_RATE).toInt();
    stMechRecord.mechTiggerTime = record.value(MECH_MECH_TIGGER_TIME).toDateTime();

    byteArray = record.value(MECH_CHAN_A_CURRENT).toByteArray();
    stMechRecord.mechChanACurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.mechChanACurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.mechChanACurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CHAN_B_CURRENT).toByteArray();
    stMechRecord.mechChanBCurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.mechChanBCurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.mechChanBCurrentLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_CHAN_C_CURRENT).toByteArray();
    stMechRecord.mechChanCCurrentLst.resize(byteArray.size() / sizeof(double));
    if (stMechRecord.mechChanCCurrentLst.count() > 0)
    {
        memcpy(&stMechRecord.mechChanCCurrentLst[0], byteArray.data(), byteArray.size());
    }

    stMechRecord.eSwitchState = MechDefine::SwitchState(record.value(MECH_SWITCH_STATE).toUInt());
    stMechRecord.dDividePeriod = record.value(MECH_DIVIDE_PERIOD).toDouble();
    stMechRecord.dJoinPeriod = record.value(MECH_JOIN_PERIOD).toDouble();
    stMechRecord.dADividePeriod = record.value(MECH_DIVIDE_PERIOD_A).toDouble();
    stMechRecord.dAJoinPeriod = record.value(MECH_JOIN_PERIOD_A).toDouble();
    stMechRecord.dBDividePeriod = record.value(MECH_DIVIDE_PERIOD_B).toDouble();
    stMechRecord.dBJoinPeriod = record.value(MECH_JOIN_PERIOD_B).toDouble();
    stMechRecord.dCDividePeriod = record.value(MECH_DIVIDE_PERIOD_C).toDouble();
    stMechRecord.dCJoinPeriod = record.value(MECH_JOIN_PERIOD_C).toDouble();
    stMechRecord.dDivideSyncPeriod = record.value(MECH_DIVIDE_SYNC_PERIOD).toDouble();
    stMechRecord.dJoinSyncPeriod = record.value(MECH_JOIN_SYNC_PERIOD).toDouble();

    stMechRecord.dTwiceDivideTime = record.value(MECH_TWICE_DIVIDE_PERIOD).toDouble();
    stMechRecord.dTwiceDivideSyncPeriod = record.value(MECH_TWICE_DIVIDE_SYNC_PERIOD).toDouble();
    stMechRecord.dTwiceADividePeriod = record.value(MECH_TWICE_DIVIDE_PERIOD_A).toDouble();
    stMechRecord.dTwiceBDividePeriod = record.value(MECH_TWICE_DIVIDE_PERIOD_B).toDouble();
    stMechRecord.dTwiceCDividePeriod = record.value(MECH_TWICE_DIVIDE_PERIOD_C).toDouble();

    stMechRecord.dASwitchShotTime = record.value(MECH_SWITCH_SHOT_TIME_A).toDouble();
    stMechRecord.dASwitchNoCurrentTime = record.value(MECH_SWITCH_NO_CURRENT_TIME_A).toDouble();
    stMechRecord.dBSwitchShotTime = record.value(MECH_SWITCH_SHOT_TIME_B).toDouble();
    stMechRecord.dBSwitchNoCurrentTime = record.value(MECH_SWITCH_NO_CURRENT_TIME_B).toDouble();
    stMechRecord.dCSwitchShotTime = record.value(MECH_SWITCH_SHOT_TIME_C).toDouble();
    stMechRecord.dCSwitchNoCurrentTime = record.value(MECH_SWITCH_NO_CURRENT_TIME_C).toDouble();

    byteArray = record.value(MECH_SWITCHSTATE_A).toByteArray();
    stMechRecord.aSwitchStateLst.resize(byteArray.size());
    if (stMechRecord.aSwitchStateLst.count() > 0)
    {
        memcpy(&stMechRecord.aSwitchStateLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_SWITCHSTATE_B).toByteArray();
    stMechRecord.bSwitchStateLst.resize(byteArray.size());
    if (stMechRecord.bSwitchStateLst.count() > 0)
    {
        memcpy(&stMechRecord.bSwitchStateLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_SWITCHSTATE_C).toByteArray();
    stMechRecord.cSwitchStateLst.resize(byteArray.size());
    if (stMechRecord.cSwitchStateLst.count() > 0)
    {
        memcpy(&stMechRecord.cSwitchStateLst[0], byteArray.data(), byteArray.size());
    }

    byteArray = record.value(MECH_SWITCH_STATE_LIST).toByteArray();
    stMechRecord.SwitchStateLst.resize(byteArray.size());
    if (stMechRecord.SwitchStateLst.count() > 0)
    {
        memcpy(&stMechRecord.SwitchStateLst[0], byteArray.data(), byteArray.size());
    }

    stMechRecord.dCloseALoopUpTime = record.value(MECH_JOIN_A_LOOP_UP_TIME).toDouble();
    stMechRecord.dCloseAHitTime = record.value(MECH_JOIN_A_HIT_TIME).toDouble();
    stMechRecord.dCloseASubSwitchCloseTime = record.value(MECH_JOIN_A_SUBSWITCH_CLOSE_TIME).toDouble();
    stMechRecord.dCloseALoopDownTime = record.value(MECH_JOIN_A_LOOP_DOWN_TIME).toDouble();
    stMechRecord.dCloseAMaxLoopCurrent = record.value(MECH_JOIN_A_LOOP_CURRENT).toDouble();

    stMechRecord.dCloseBLoopUpTime = record.value(MECH_JOIN_B_LOOP_UP_TIME).toDouble();
    stMechRecord.dCloseBHitTime = record.value(MECH_JOIN_B_HIT_TIME).toDouble();
    stMechRecord.dCloseBSubSwitchCloseTime = record.value(MECH_JOIN_B_SUBSWITCH_CLOSE_TIME).toDouble();
    stMechRecord.dCloseBLoopDownTime = record.value(MECH_JOIN_B_LOOP_DOWN_TIME).toDouble();
    stMechRecord.dCloseBMaxLoopCurrent = record.value(MECH_JOIN_B_LOOP_CURRENT).toDouble();

    stMechRecord.dCloseCLoopUpTime = record.value(MECH_JOIN_C_LOOP_UP_TIME).toDouble();
    stMechRecord.dCloseCHitTime = record.value(MECH_JOIN_C_HIT_TIME).toDouble();
    stMechRecord.dCloseCSubSwitchCloseTime = record.value(MECH_JOIN_C_SUBSWITCH_CLOSE_TIME).toDouble();
    stMechRecord.dCloseCLoopDownTime = record.value(MECH_JOIN_C_LOOP_DOWN_TIME).toDouble();
    stMechRecord.dCloseCMaxLoopCurrent = record.value(MECH_JOIN_C_LOOP_CURRENT).toDouble();

    stMechRecord.dOpenALoopUpTime = record.value(MECH_DEVICE_A_LOOP_UP_TIME).toDouble();
    stMechRecord.dOpenAHitTime = record.value(MECH_DEVICE_A_HIT_TIME).toDouble();
    stMechRecord.dOpenASubSwitchCloseTime = record.value(MECH_DEVICE_A_SUBSWITCH_CLOSE_TIME).toDouble();
    stMechRecord.dOpenALoopDownTime = record.value(MECH_DEVICE_A_LOOP_DOWN_TIME).toDouble();
    stMechRecord.dOpenAMaxLoopCurrent = record.value(MECH_DEVICE_A_LOOP_CURRENT).toDouble();

    stMechRecord.dOpenBLoopUpTime = record.value(MECH_DEVICE_B_LOOP_UP_TIME).toDouble();
    stMechRecord.dOpenBHitTime = record.value(MECH_DEVICE_B_HIT_TIME).toDouble();
    stMechRecord.dOpenBSubSwitchCloseTime = record.value(MECH_DEVICE_B_SUBSWITCH_CLOSE_TIME).toDouble();
    stMechRecord.dOpenBLoopDownTime = record.value(MECH_DEVICE_B_LOOP_DOWN_TIME).toDouble();
    stMechRecord.dOpenBMaxLoopCurrent = record.value(MECH_DEVICE_B_LOOP_CURRENT).toDouble();

    stMechRecord.dOpenCLoopUpTime = record.value(MECH_DEVICE_C_LOOP_UP_TIME).toDouble();
    stMechRecord.dOpenCHitTime = record.value(MECH_DEVICE_C_HIT_TIME).toDouble();
    stMechRecord.dOpenCSubSwitchCloseTime = record.value(MECH_DEVICE_C_SUBSWITCH_CLOSE_TIME).toDouble();
    stMechRecord.dOpenCLoopDownTime = record.value(MECH_DEVICE_C_LOOP_DOWN_TIME).toDouble();
    stMechRecord.dOpenCMaxLoopCurrent = record.value(MECH_DEVICE_C_LOOP_CURRENT).toDouble();

    stMechRecord.dTwiceOpenALoopUpTime = record.value(MECH_TWICE_DEVICE_A_LOOP_UP_TIME).toDouble();
    stMechRecord.dTwiceOpenAHitTime = record.value(MECH_TWICE_DEVICE_A_HIT_TIME).toDouble();
    stMechRecord.dTwiceOpenASubSwitchCloseTime = record.value(MECH_TWICE_DEVICE_A_SUBSWITCH_CLOSE_TIME).toDouble();
    stMechRecord.dTwiceOpenALoopDownTime = record.value(MECH_TWICE_DEVICE_A_LOOP_DOWN_TIME).toDouble();
    stMechRecord.dTwiceOpenAMaxLoopCurrent = record.value(MECH_TWICE_DEVICE_A_LOOP_CURRENT).toDouble();

    stMechRecord.dTwiceOpenBLoopUpTime = record.value(MECH_TWICE_DEVICE_B_LOOP_UP_TIME).toDouble();
    stMechRecord.dTwiceOpenBHitTime = record.value(MECH_TWICE_DEVICE_B_HIT_TIME).toDouble();
    stMechRecord.dTwiceOpenBSubSwitchCloseTime = record.value(MECH_TWICE_DEVICE_B_SUBSWITCH_CLOSE_TIME).toDouble();
    stMechRecord.dTwiceOpenBLoopDownTime = record.value(MECH_TWICE_DEVICE_B_LOOP_DOWN_TIME).toDouble();
    stMechRecord.dTwiceOpenBMaxLoopCurrent = record.value(MECH_TWICE_DEVICE_B_LOOP_CURRENT).toDouble();

    stMechRecord.dTwiceOpenCLoopUpTime = record.value(MECH_TWICE_DEVICE_C_LOOP_UP_TIME).toDouble();
    stMechRecord.dTwiceOpenCHitTime = record.value(MECH_TWICE_DEVICE_C_HIT_TIME).toDouble();
    stMechRecord.dTwiceOpenCSubSwitchCloseTime = record.value(MECH_TWICE_DEVICE_C_SUBSWITCH_CLOSE_TIME).toDouble();
    stMechRecord.dTwiceOpenCLoopDownTime = record.value(MECH_TWICE_DEVICE_C_LOOP_DOWN_TIME).toDouble();
    stMechRecord.dTwiceOpenCMaxLoopCurrent = record.value(MECH_TWICE_DEVICE_C_LOOP_CURRENT).toDouble();

    stMechRecord.dAMechStartCurrent = record.value(MECH_CHAN_A_START_CURRENT).toDouble();
    stMechRecord.dAMaxMechCurrent = record.value(MECH_CHAN_A_MAX_CURRENT).toDouble();
    stMechRecord.dAMechStoragePeriod = record.value(MECH_CHAN_A_STOREAGE_PERIOD).toDouble();

    stMechRecord.dBMechStartCurrent = record.value(MECH_CHAN_B_START_CURRENT).toDouble();
    stMechRecord.dBMaxMechCurrent = record.value(MECH_CHAN_B_MAX_CURRENT).toDouble();
    stMechRecord.dBMechStoragePeriod = record.value(MECH_CHAN_B_STOREAGE_PERIOD).toDouble();

    stMechRecord.dCMechStartCurrent = record.value(MECH_CHAN_C_START_CURRENT).toDouble();
    stMechRecord.dCMaxMechCurrent = record.value(MECH_CHAN_C_MAX_CURRENT).toDouble();
    stMechRecord.dCMechStoragePeriod = record.value(MECH_CHAN_C_STOREAGE_PERIOD).toDouble();

    return stMechRecord;
}

/*************************************************
功能： 从数据库中获取最新一条机械特性记录数据
输出参数：
        stMechRecord：机械特性记录数据结构
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::getLastestMechRecord( const QString &strPointName, MechDefine::MechISRecord &stMechRecord )
{
    MechDB* pdb = (MechDB*)m_mapDBs[DB_MECH];

    bool bHasRecord = false;

    DataRecord record = pdb->lastRecord(strPointName);

    if( record.isValid() )
    {
        bHasRecord = true;
        stMechRecord = getMECHRecord(record);
    }

    return bHasRecord;
}

/*************************************************
功能： 从数据库中获取最新一条机械特性记录数据
输出参数：
        stMechRecord：机械特性记录数据结构
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::getMechRecord( const QString &strPointName,int dataID, MechDefine::MechISRecord &stMechRecord )
{
    MechDB* pdb = (MechDB*)m_mapDBs[DB_MECH];

    bool bHasRecord = false;

    DataRecord record = pdb->getMechRecord(strPointName, dataID);

    if( record.isValid() )
    {
        bHasRecord = true;
        stMechRecord = getMECHRecord(record);
    }

    return bHasRecord;
}

/*************************************************
功能： 获取某通道时间范围内的某种类型趋势数据 -- （趋势查询）
输入参数：
        strChannel -- 通道名称
        eType --趋势图类型
        timeStart -- 开始时间
        timeEnd -- 结束时间
        iLimit -- 趋势数据限制
返回值：
        趋势数据列表
*************************************************************/
QList<float> DBServer::trendData( const QString &strChannel, TrendType eType, const QDateTime &timeStart, const QDateTime &timeEnd,
                        int iLimit )
{
    DataBaseServiceType eDB = getDatabaseType( eType );
    int iItemKey = getItemKey( eType );

    if( m_mapDBs.contains( eDB ) )
    {
        PDDB* pdb = (PDDB*)m_mapDBs[ eDB ];

        return pdb->trendData( strChannel, iItemKey, timeStart, timeEnd, iLimit );
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::trendData, invalid db type: %d", eDB);
        return QList<float>();
    }
}

bool DBServer::getArresterTrendData(const QString &strPointID, ChannelPhase eChannelPhase, const QDateTime &startDate, const QDateTime &endDate, QList<QDateTime> &listDateTime, QList<float> &listData, int iLimit)
{
    QDateTime startTime = startDate;
    QDateTime endTime = endDate;
    ArresterDB* pdb = (ArresterDB*)m_mapDBs[ DB_ARRESTER ];

    return pdb->getTrendData(strPointID, eChannelPhase, startTime, endTime, listDateTime, listData, iLimit);
}
/*************************************************
功能： 获取某通道时间范围内的某种类型趋势数据 -- （趋势查询）
输入参数：
        strPointID -- 通道名称
        eType --趋势图类型
        timeStart -- 开始时间
        timeEnd -- 结束时间
        iLimit -- 趋势数据限制
返回值：
        趋势数据列表
*************************************************************/
bool DBServer::getTrendData(const QString &strPointID, ADUChannelType eChannelType, const QDateTime &startDate, const QDateTime &endDate, QList<QDateTime> &listDateTime, QList<float> &listData,int iLimit)
{
    PDS_SYS_INFO_LOG("getTrendData start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("getTrendData start");});

    QDateTime startTime = startDate;
    QDateTime endTime = endDate;

    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    if (getTrendData(eDB, strPointID, startDate, endDate, iLimit, &listDateTime, &listData))
    {
        return true;
    }

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];
        int iItemKey;
        TestPointInfo stTestPoint;
        if(!m_configService->getTestPoint(strPointID, stTestPoint))
        {
            return false;
        }

        QString strPointName = strPointID;
        ADUType eADUType;
        int iTableID = SINGLE_TABLE;

        int iLastRecordId = lastRecordId(strPointID, eChannelType);
        if (DbconfigUtils::hasCountTable(eChannelType) &&
                !m_configService->getISADUType(stTestPoint.ConnectionInfo.first().strID, eADUType) &&
                !m_configService->getISMoistureADUType(stTestPoint.ConnectionInfo.first().strID, eADUType) &&
                (100 < iLastRecordId))
        {
            iTableID = TREND_COUNT_TABLE;
            strPointName = COUNT_TABLE_NAME + strPointID;
            switch (eChannelType)
            {
//            case CHANNEL_TEV://TEV参数
//            {
//                iItemKey = TEV_TREND_COUNT;
//            }
//                break;
            case CHANNEL_TEMPERATURE:
            {
                iItemKey = TEMP_TREND_COUNT;
            }
                break;
            case CHANNEL_HUMIDITY:
            {
                iItemKey = HUMI_TREND_COUNT;
            }
                break;
            case CHANNEL_FROST_RAW:
            {
                iItemKey = FROST_RAW_TREND_COUNT;
            }
                break;
            case CHANNEL_FROST_ATM:
            {
                iItemKey = FROST_ATM_TREND_COUNT;
            }
                break;
            case CHANNEL_DEW_RAW:
            {
                iItemKey = DEW_RAW_TREND_COUNT;
            }
                break;
            case CHANNEL_DEW_ATM:
            {
                iItemKey = DEW_ATM_TREND_COUNT;
            }
                break;
            case CHANNEL_MOISTURE:
            {
                iItemKey = MOISTURE_TREND_COUNT;
            }
                break;
            case CHANNEL_PRESS_ABSO:
            {
                iItemKey = PRESS_ABSO_TREND_COUNT;
            }
                break;
            case CHANNEL_PRESS_NORM:
            {
                iItemKey = PRESS_NORM_TREND_COUNT;
            }
                break;
            case CHANNEL_DENSITY:
            {
                iItemKey = DENSITY_TREND_COUNT;
            }
                break;
            case CHANNEL_OXYGEN:
            {
                iItemKey = OXYGEN_TREND_COUNT;
            }
                break;
            case CHANNEL_SF6:
            {
                iItemKey = SF6_TREND_COUNT;
            }
            default:
                return false;
                break;
            }
        }
        else
        {
            switch (eChannelType)
            {
//            case CHANNEL_AE:
//            {
//                iItemKey = AE_MAX;
//            }
//                break;
//            case CHANNEL_TEV://TEV参数
//            {
//                iItemKey = TEV_MAX;
//            }
//                break;
            case CHANNEL_TEMPERATURE:
            {
                iItemKey = TEMP_DATA;
            }
                break;
            case CHANNEL_HUMIDITY:
            {
                iItemKey = HUMI_DATA;
            }
                break;
            case CHANNEL_ARRESTER_I:
            case CHANNEL_GROUNDDINGCURRENT:
            case CHANNEL_LEAKAGECURRENT:
            {
                iItemKey = FROST_RAW_DATA;
            }
                break;
            case CHANNEL_FROST_ATM:
            {
                iItemKey = FROST_ATM_DATA;
            }
                break;
            case CHANNEL_DEW_RAW:
            {
                iItemKey = DEW_RAW_DATA;
            }
                break;
            case CHANNEL_DEW_ATM:
            {
                iItemKey = DEW_ATM_DATA;
            }
                break;
            case CHANNEL_MOISTURE:
            {
                iItemKey = MOISTURE_DATA;
            }
                break;
            case CHANNEL_PRESS_ABSO:
            {
                iItemKey = PRESS_ABSO_DATA;
            }
                break;
            case CHANNEL_PRESS_NORM:
            {
                iItemKey = PRESS_NORM_DATA;
            }
                break;
            case CHANNEL_DENSITY:
            {
                iItemKey = DENSITY_DATA;
            }
                break;
            case CHANNEL_OXYGEN:
            {
                iItemKey = OXYGEN_DATA;
            }
                break;
            case CHANNEL_SF6:
            {
                iItemKey = SF6_DATA;
            }
                break;

            case CHANNEL_FLOOD:
            {
                iItemKey = ENV_STATUS;
            }
                break;
            case CHANNEL_FIREWORKS_ALARM:
            {
                iItemKey = ENV_STATUS;
            }
                break;
            case CHANNEL_HIKVIDEO:
            {
                iItemKey = HK_MAX_TEMPRATURE;
            }
                break;
            default:
                return false;
            }
        }

        return pdb->getTrendData(strPointName, iItemKey, startTime, endTime, listDateTime, listData, iTableID, iLimit);
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::nearbyRecordId, invalid db type: %d", eDB);
        return false;
    }
}

bool DBServer::getTrendData(const QString &pointId, const QDateTime &startDate, const QDateTime &endDate, uint64_t limit, QVariantList *datalist) const
{
    ADUChannelType channelType = CHANNEL_INVALID;
    {
        QString aduId;
        quint8 channelId = 0;
        auto status = Status::OK();
        if (!(status = StorageUtils::getAllAduInfoFromPointId(pointId, &aduId, &channelId, &channelType)).ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
    }
    return getTrendDataRecords(channelType, pointId, startDate, endDate, limit, datalist);
}

bool DBServer::getHistoryData( const QString &strPointID,
                     ADUChannelType eChannelType,
                     const QDateTime &startDate,
                     const QDateTime &endDate,
                     QList<QDateTime> &listDateTime,
                     QList<float> &listData,
                     QList<QString> &listDataID,
                     QList<int> &listSensorType,
                     int &iDataCounts,
                     int iPageSize, int iPageCount)
{
    QDateTime startTime = startDate;
    QDateTime endTime = endDate.addDays(1).addMSecs(-1);//调整时间为结束日期的当日23:59:59.999

    DataBaseServiceType eDB = getDatabaseType( eChannelType );

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];
        int iItemKey;
        int iTableID = SINGLE_TABLE;

        switch (eChannelType)
        {
        case CHANNEL_TEMPERATURE:
        {
            iItemKey = TEMP_DATA;
        }
            break;
        case CHANNEL_HUMIDITY:
        {
            iItemKey = HUMI_DATA;
        }
            break;
        case CHANNEL_FROST_RAW:
        {
            iItemKey = FROST_RAW_DATA;
        }
            break;
        case CHANNEL_FROST_ATM:
        {
            iItemKey = FROST_ATM_DATA;
        }
            break;
        case CHANNEL_DEW_RAW:
        {
            iItemKey = DEW_RAW_DATA;
        }
            break;
        case CHANNEL_DEW_ATM:
        {
            iItemKey = DEW_ATM_DATA;
        }
            break;
        case CHANNEL_MOISTURE:
        {
            iItemKey = MOISTURE_DATA;
        }
            break;
        case CHANNEL_PRESS_ABSO:
        {
            iItemKey = PRESS_ABSO_DATA;
        }
            break;
        case CHANNEL_PRESS_NORM:
        {
            iItemKey = PRESS_NORM_DATA;
        }
            break;
        case CHANNEL_DENSITY:
        {
            iItemKey = DENSITY_DATA;
        }
            break;
        case CHANNEL_OXYGEN:
        {
            iItemKey = OXYGEN_DATA;
        }
            break;
        case CHANNEL_SF6:
        {
            iItemKey = SF6_DATA;
        }
            break;

        default:
            return false;
            break;
        }

        return pdb->getHistoryData(strPointID, iItemKey,
                                   startTime, endTime,
                                   listDateTime,
                                   listData,
                                   listDataID,
                                   listSensorType,
                                   iDataCounts,
                                   iTableID,
                                   iPageSize, iPageCount );
    }
    else
    {
        PDS_SYS_WARNING_LOG("[DBServer::getHistoryData]:DBServer::nearbyRecordId, invalid db type: %d", eDB);
        return false;
    }
}

QVector<AERecord> DBServer::getAeScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    PDS_SYS_INFO_LOG("DBServer::getAeScanData start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getAeScanData start");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordInTimePeriod(pointId, start, end, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::AERecord>>(tmpdatas, [this, &pointId](const common::DataWrapper& input)
    {
       auto unwrapData = input.getData<common::PdAeData>();
       return unwrapData ? DataTypeConvertionUtils::convertFromPdAeData(pointId, *unwrapData) : ::AERecord{};
    });
}

QVector<TEVRecord> DBServer::getTevScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    PDS_SYS_INFO_LOG("DBServer::getTevScanData start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getTevScanData end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordInTimePeriod(pointId, start, end, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::TEVRecord>>(tmpdatas, [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdTevData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdTevData(pointId, *unwrapData) : ::TEVRecord{};
    });
}

QVector<PRPSRecord> DBServer::getUhfScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    PDS_SYS_INFO_LOG("DBServer::getUhfScanData start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getUhfScanData end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordInTimePeriod(pointId, start, end, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::PRPSRecord>>(tmpdatas, [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdUhfData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdUhfData(pointId, *unwrapData) : ::PRPSRecord{};
    });
}

QVector<PRPSRecord> DBServer::getHfctScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    PDS_SYS_INFO_LOG("DBServer::getHfctScanData start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getHfctScanData end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordInTimePeriod(pointId, start, end, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::PRPSRecord>>(tmpdatas, [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdHfctData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdHfctData(pointId, *unwrapData) : ::PRPSRecord{};
    });
}

QVector<MechDefine::MechISRecord> DBServer::getMechScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    return transformSequence<QVector<MechDefine::MechISRecord>>(getDataByScanTime(DB_MECH, pointId, start, end),
                                                [this](const DataRecord& record){return getMECHRecord(record);});
}

QVector<EnvStatusRecord> DBServer::getWaterLoggingScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    return transformSequence<QVector<EnvStatusRecord>>(getDataByScanTime(DB_ENV_STATUS, pointId, start, end),
                                                [this](const DataRecord& record){return getEnvStatusRecord(record);});
}

QVector<Sf6StateRecord> DBServer::getSf6StateScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    return transformSequence<QVector<Sf6StateRecord>>(getDataByScanTime(DB_SF6_STATE, pointId, start, end),
                                                [this](const DataRecord& record){return getSf6StateRecord(record);});
}

QVector<TEMPRecord> DBServer::getTempratureScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    return transformSequence<QVector<TEMPRecord>>(getDataByScanTime(DB_TEMP, pointId, start, end),
                                                [this](const DataRecord& record){return getTEMPRecord(record);});
}

QVector<HumidityRecord> DBServer::getHumanityScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    return transformSequence<QVector<HumidityRecord>>(getDataByScanTime(DB_HUMI, pointId, start, end),
                                                      [this](const DataRecord& record){return getHumidityRecord(record);});
}

QVector<HkVideoRecord> DBServer::getHkVideoScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    return transformSequence<QVector<HkVideoRecord>>(getDataByScanTime(DB_HK_VIDEO, pointId, start, end),
                                                      [this](const DataRecord& record){return getHkVideoRecord(record);});
}

/*************************************************
功能： 根据趋势数据类型，获取数据库类型
输入参数：
    eType -- 趋势数据类型
返回值： 数据库类型
*************************************************************/
DataBaseServiceType DBServer::getDatabaseType( TrendType eType )
{
    DataBaseServiceType eDB = DB_INVALID;

    switch( eType )
    {
        default:
            break;
    }

    return eDB;
}

/*************************************************
功能： 根据通道类型，获取数据库类型
输入参数：
    eType -- 通道类型
返回值： 数据库类型
*************************************************************/
DataBaseServiceType DBServer::getDatabaseType( PDChannelType eType )
{
    DataBaseServiceType eDB = DB_INVALID;

    switch( eType )
    {
    case PDCHANNEL_AE:
    {
        eDB = DB_AE;
    }
        break;
    case PDCHANNEL_TEV:
    {
        eDB = DB_TEV;
    }
        break;
    case PDCHANNEL_UHFPRPS:
    {
        eDB = DB_UHFPRPS;
    }
        break;
    case PDCHANNEL_HFCTPRPS:
    {
        eDB = DB_HFCTPRPS;
    }
        break;
    default:
        break;
    }

    return eDB;
}
DataBaseServiceType DBServer::getDatabaseType(ADUChannelType eType )
{
    DataBaseServiceType eDB = DB_INVALID;

    switch( eType )
    {
    case CHANNEL_AE:
    {
        eDB = DB_AE;
    }
        break;
    case CHANNEL_TEV:
    {
        eDB = DB_TEV;
    }
        break;
    case CHANNEL_UHF:
    {
        eDB = DB_UHFPRPS;
    }
        break;
    case CHANNEL_HFCT:
    {
        eDB = DB_HFCTPRPS;
    }
        break;
    case CHANNEL_TEVPRPS:
    {
        eDB = DB_TEVPRPS;
    }
        break;
    case CHANNEL_MECH:
    {
        eDB = DB_MECH;
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        eDB = DB_TEMP;
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        eDB = DB_HUMI;
    }
        break;
    case CHANNEL_ARRESTER_I:
    case CHANNEL_ARRESTER_U:
    case CHANNEL_GROUNDDINGCURRENT:
    case CHANNEL_LEAKAGECURRENT:
    {
        eDB = DB_ARRESTER;
    }
        break;
    case CHANNEL_VIBRATION:
    {
        eDB = DB_VIBRATION;
    }
        break;

    //微水数据库:
    case CHANNEL_FROST_RAW:
    {
        eDB = DB_FROST_RAW;
    }
        break;

    case CHANNEL_FROST_ATM:
    {
        eDB = DB_FROST_ATM;
    }
        break;

    case CHANNEL_DEW_RAW:
    {
        eDB = DB_DEW_RAW;
    }
        break;

    case CHANNEL_DEW_ATM:
    {
        eDB = DB_DEW_ATM;
    }
        break;

    case CHANNEL_MOISTURE:
    {
        eDB = DB_MOISTURE;
    }
        break;

    case CHANNEL_PRESS_ABSO:
    {
        eDB = DB_PRESS_ABSO;
    }
        break;

    case CHANNEL_PRESS_NORM:
    {
        eDB = DB_PRESS_NORM;
    }
        break;

    case CHANNEL_DENSITY:
    {
        eDB = DB_DENSITY;
    }
        break;

    case CHANNEL_OXYGEN:
    {
        eDB = DB_OXYGEN;
    }
        break;

    case CHANNEL_SF6:
    {
        eDB = DB_SF6_STATE;
    }
        break;
    case CHANNEL_LOWTENSVG:
    {
        eDB = DB_METER;
    }
        break;
    case CHANNEL_TENSION:
    {
        eDB = DB_LV_METER;
    }
        break;
    case CHANNEL_AIR_CONDITIONER:
    {
        eDB = DB_CACC;
    }
        break;
    case CHANNEL_NOISE:
    case CHANNEL_NOISE_SH:
    case CHANNEL_FIREWORKS_ALARM:
    case CHANNEL_WATER:
    case CHANNEL_FLOOD:
    case CHANNEL_IR_DETECTION:
    case CHANNEL_LIGHT:
    case CHANNEL_FAN:
    {
        eDB = DB_ENV_STATUS;
    }
        break;
    case CHANNEL_LIQUID_LEVEL:
    {
        eDB = DB_WATER_LEVEL;
    }
        break;
    case CHANNEL_OPTICAL_TEMP:
    {
        eDB = DB_FIBER_TEMP;
    }
        break;

    case CHANNEL_SF6_GAS:
    {
        eDB = DB_SF6_STATE;
    }
        break;

    case CHANNEL_HIKVIDEO:
    {
        eDB = DB_HK_VIDEO;
        break;
    }
    default:
        break;
    }

    return eDB;
}
DataBaseServiceType DBServer::getDatabaseType(LNTYPE eType )
{
    DataBaseServiceType eDB = DB_INVALID;

    switch( eType )
    {
    case MMXU:
    {
        eDB = DB_METER;
    }
        break;
    case LVMETER:
    {
        eDB = DB_LV_METER;
    }
        break;
    case CACC:
    {
        eDB = DB_CACC;
    }
        break;
    case SOUND:
    case SMOKE:
    case SWLA:
    {
        eDB = DB_ENV_STATUS;
    }
        break;
    case WATER:
    {
        eDB = DB_WATER_LEVEL;
    }
        break;
    case CLSL:
    case KFAN:
    {
        eDB = DB_LOW_TENSION_SWITCH;
    }
        break;
    case FIBER:
    {
        eDB = DB_FIBER_TEMP;
    }
        break;
    case STMP:
    {
        eDB = DB_TEMP;
    }
        break;
    default:
        break;
    }

    return eDB;
}

/*************************************************
功能： 根据趋势数据类型，获取条目键值
输入参数：
    eType -- 趋势数据类型
返回值： 条目键值
*************************************************************/
int DBServer::getItemKey( TrendType eType )
{
    int iItemKey = -1;

    switch( eType )
    {
        default:
            break;
    }

    return iItemKey;
}

/*************************************************
功能： 查询通道有数据的月份/日期
输入参数：
        eType -- 通道类型
        strChannel -- 通道名称
        timeStart -- 开始时间
        timeEnd -- 结束时间
返回值：
        有数据的时间列表
*************************************************************/
QList<QDate> DBServer::monthsHasData(PDChannelType eType, const QString& strChannel, const QDateTime& timeStart, const QDateTime& timeEnd )
{
    DataBaseServiceType eDB = getDatabaseType( eType );

    if( m_mapDBs.contains( eDB ) )
    {
        PDDB* pdb = (PDDB*)m_mapDBs[ eDB ];

        return pdb->monthsHasData( strChannel, timeStart, timeEnd );
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::monthsHasData, invalid db type: %d", eDB);

        return QList<QDate>();
    }
}
QList<QDate> DBServer::daysHasData( PDChannelType eType, const QString& strChannel, const QDateTime& timeStart, const QDateTime& timeEnd )
{
    DataBaseServiceType eDB = getDatabaseType( eType );

    if( m_mapDBs.contains( eDB ) )
    {
        PDDB* pdb = (PDDB*)m_mapDBs[ eDB ];

        return pdb->daysHasData( strChannel, timeStart, timeEnd );
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::daysHasData, invalid db type: %d", eDB);

        return QList<QDate>();
    }
}
QList<QDate> DBServer::monthsHasData(ADUChannelType eType, const QString& strChannel, const QDateTime& timeStart, const QDateTime& timeEnd )
{
    DataBaseServiceType eDB = getDatabaseType( eType );

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        return pdb->monthsHasData( strChannel, timeStart, timeEnd );
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::monthsHasData, invalid db type: %d", eDB);
        return QList<QDate>();
    }

}
QList<QDate> DBServer::daysHasData( ADUChannelType eType, const QString& strChannel, const QDateTime& timeStart, const QDateTime& timeEnd )
{
    DataBaseServiceType eDB = getDatabaseType( eType );

    if( m_mapDBs.contains( eDB ) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];

        return pdb->daysHasData( strChannel, timeStart, timeEnd );
    }
    else
    {
        PDS_SYS_WARNING_LOG("DBServer::daysHasData, invalid db type: %d", eDB);

        return QList<QDate>();
    }
}

///*************************************************
//功能： 获取通道某天时间范围内的数据
//输入参数：
//        eType -- 通道类型
//        strChannel -- 通道名称
//        day -- 时间
//        timeStart -- 开始时间
//        timeEnd -- 结束时间
//        iLimit -- 趋势数据限制
//返回值：
//        数据列表
//*************************************************************/
//QVariant DBServer::recordsInDay( PDChannelType eType, const QString& strChannel, const QDateTime& timeStart, const QDateTime& timeEnd )
//{
//    DataBaseServiceType eDB = getDatabaseType( eType );

//    if( m_mapDBs.contains( eDB ) )
//    {
//        PDDB* pdb = (PDDB*)m_mapDBs[ eDB ];
//        DataRecordList records = pdb->recordsInDay( strChannel, timeStart, timeEnd );

//        return getRecordsByDataRecords( records, eType );
//    }
//    else
//    {
//        DBWarning() << "DBServer::recordsInDay, invalid db type " << eDB;

//        return QVariant();
//    }
//}

///*************************************************
//功能： 获取通道某天时间范围内的数据
//输入参数：
//        eChannelType -- 通道类型
//        strPointID -- 通道名称
//        timeStart -- 开始时间
//        timeEnd -- 结束时间
//输出参数:
//        listAutoID -- 序号列表
//        listRecordTime -- 时间列表
//返回值：
//        数据列表
//*************************************************************/
//void DBServer::recordsInDay(ADUChannelType eChannelType, const QString &strPointID, QList<int> &listAutoID, QList<QDateTime> &listRecordTime, QDateTime startTime, QDateTime endTime)
//{
//    if (!startTime.isValid())
//    {
//        startTime = QDateTime::fromString("2000","yyyy");
//    }
//    DataBaseServiceType eDB = getDatabaseType( eChannelType );

//    if( m_mapDBs.contains( eDB ) )
//    {
//        SqliteDB* pdb = (SqliteDB*)m_mapDBs[ eDB ];
//        if (!endTime.isValid())
//        {
//            endTime = QDateTime::fromTime_t(pdb->lastRecordTimeT(strPointID)).addSecs(20);
//        }

//        pdb->recordsInDay(strPointID, listAutoID, listRecordTime, startTime, endTime);
//    }
//    else
//    {
//        DBWarning() << "DBServer::recordsInDay, invalid db type " << eDB;
//    }
//}

/*************************************************
功能： 将数据记录转换为QVariant
输入参数：
        dbRecords -- 数据记录集
        eType -- 通道类型
返回值：
        QVariant（QList<AERecord>...)
*************************************************************/
//QVariant DBServer::getRecordsByDataRecords( const DataRecordList& dbRecords, PDChannelType eType )
//{
//    QVariant data;

//    switch( eType )
//    {
//        case PDCHANNEL_AE:
//        {
//            QList<AERecord> pdRecords;
//            for( int i=0; i<dbRecords.count(); i++ )
//            {
//                pdRecords << getAERecord( dbRecords.at(i) );
//            }
//            data = qVariantFromValue( pdRecords );
//        }
//            break;
//        case PDCHANNEL_TEV:
//        {
//            QList<TEVRecord> pdRecords;
//            for( int i=0; i<dbRecords.count(); i++ )
//            {
//                pdRecords << getTEVRecord( dbRecords.at(i) );
//            }
//            data = qVariantFromValue( pdRecords );
//        }
//            break;
//        case PDCHANNEL_UHFPRPS:
//        {
//            QList<PRPSRecord> pdRecords;
//            for( int i=0; i<dbRecords.count(); i++ )
//            {
//                pdRecords << getUHFPRPSRecord( dbRecords.at(i) );
//            }
//            data = qVariantFromValue( pdRecords );
//        }
//            break;
//        case PDCHANNEL_HFCTPRPS:
//        {
//            QList<PRPSRecord> pdRecords;
//            for( int i=0; i<dbRecords.count(); i++ )
//            {
//                pdRecords << getHFCTPRPSRecord( dbRecords.at(i) );
//            }
//            data = qVariantFromValue( pdRecords );
//        }
//            break;
//        default:
//            break;
//    }

//    return data;
//}

///*************************************************
//功能： 将数据记录转换为局放记录
//输入参数：
//        dbRecord -- 数据记录集
//返回值：
//        局放记录
//*************************************************************/
//AERecord DBServer::getAERecord(const DataRecord& dbRecord) const
//{
////    AERecord record;

////    record.recordtime = dbRecord.value(IK_RECORDTIME).toDateTime();
////    record.autoid = dbRecord.value(IK_ID).toInt();
////    record.recordstringid = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
////    record.bIsUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
////    record.stPointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
////    record.stPointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
////    record.stPointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
////    record.stPointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
////    record.stPointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
////    record.batteryinfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
////    record.recordid = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
////    record.globalid = dbRecord.value(IK_GLOBAL_ID).toLongLong();
////    record.channelid = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduid);
////    record.fFre1 = dbRecord.value(AE_FRE1_VALUE).toFloat();
////    record.fFre2 = dbRecord.value(AE_FRE2_VALUE).toFloat();
////    record.fMax = dbRecord.value(AE_MAX).toFloat();
////    record.fRms = dbRecord.value(AE_RMS).toFloat();
////    record.ucGain = dbRecord.value(AE_GAIN).toUInt();
////    record.ucPwrFre = dbRecord.value(AE_FREQUENCY).toUInt();
////    record.ucSyncFlag = dbRecord.value(AE_SYNC_FLAG).toUInt();
////    record.ucSyncType = dbRecord.value(AE_SYNC_TYPE).toUInt();
////    record.ucSampleCycleCount = dbRecord.value(AE_SAMPLE_COUNT).toUInt();
////    record.usSampleCountCycle = dbRecord.value(AE_SAMPLE_RATE).toUInt();
////    QByteArray array = dbRecord.value(AE_ARRAY).toByteArray();
////    if (array.size() >= 4)
////    {
////        record.fArray.resize(array.count()/sizeof(float));
////        memcpy( &record.fArray[0], array.data(), array.count() );
////    }

////    return record;
//}

///*************************************************
//功能： 将数据记录转换为局放记录
//输入参数：
//        dbRecord -- 数据记录集
//返回值：
//        局放记录
//*************************************************************/
//TEVRecord DBServer::getTEVRecord( const DataRecord& dbRecord ) const
//{
//    TEVRecord record;
//    record.recordtime = dbRecord.value(IK_RECORDTIME).toDateTime();
//    record.autoid = dbRecord.value(IK_ID).toInt();
//    record.recordstringid = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
//    record.bIsUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
//    record.stPointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
//    record.stPointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
//    record.stPointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
//    record.stPointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
//    record.stPointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
//    record.batteryinfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
//    record.recordid = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
//    record.globalid = dbRecord.value(IK_GLOBAL_ID).toLongLong();
//    record.channelid = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduid);
//    record.cMax = dbRecord.value(TEV_MAX).toInt();

//    return record;
//}

/*************************************************
功能： 将数据记录转换为局放记录
输入参数：
        dbRecord -- 数据记录集
返回值：
        局放记录
*************************************************************/
TEMPRecord DBServer::getTEMPRecord( const DataRecord& dbRecord ) const
{
    TEMPRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();

    const int iBattery = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    const int iRemainTime = dbRecord.value(SAMPLE_DATE_REMAIN_TIME).toInt();
    record.batteryInfo = iBattery + iRemainTime * 10;
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();

    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);
//    record.temprature = dbRecord.value(TEMP_DATA).toFloat();
    retriveData(dbRecord.value(TEMP_DATA).toByteArray(), &record);

    return record;
}

/*************************************************
功能： 将数据记录转换为局放记录
输入参数：
        dbRecord -- 数据记录集
返回值：
        局放记录
*************************************************************/
HumidityRecord DBServer::getHumidityRecord( const DataRecord& dbRecord ) const
{
    HumidityRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();

    const int iBattery = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    const int iRemainTime = dbRecord.value(SAMPLE_DATE_REMAIN_TIME).toInt();
    record.batteryInfo = iBattery + iRemainTime * 10;
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();

    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);
//    record.humanity = dbRecord.value(HUMI_DATA).toFloat();
    retriveData(dbRecord.value(HUMI_DATA).toByteArray(), &record);

    return record;
}

/*************************************************
功能： 将数据记录转换为避雷器记录
输入参数：
        dbRecord -- 数据记录集
返回值：
        避雷器记录
*************************************************************/
ArresterRecord DBServer::getArresterRecord( const DataRecord& dbRecord ) const
{
    ArresterRecord record;
    record.datetime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.iautoId = dbRecord.value(IK_ID).toInt();
    record.strRecordID = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.bIsUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.stPointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.stPointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.stPointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.stPointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.stPointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.uiBattery = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.usRecordID = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.illGlobalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.ucChannelID = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.strADUID);

    record.eChannelPhase = (ChannelPhase)dbRecord.value(ARRESTER_CHANNEL_PHASE).toInt();
    record.ucFrequency = (quint8)dbRecord.value(ARRESTER_FREQUENCY).toInt();
    record.fResistiveCurrent = dbRecord.value(ARRESTER_DATA_RESISTIVE_CURRENT).toFloat();
    record.fLeakageCurrent = dbRecord.value(ARRESTER_DATA_LEAKAGE_CURRENT_CURRENT).toFloat();

    QByteArray array;
    array = dbRecord.value(ARRESTER_DATA_I_CHANNEL).toByteArray();
    if (array.size() >= 4)
    {
        record.vecCurrentDataChannel.resize(array.count()/sizeof(float));
        memcpy( &record.vecCurrentDataChannel[0], array.data(), array.count() );
    }

    array = dbRecord.value(ARRESTER_DATA_U_CHANNEL).toByteArray();
    if (array.size() >= 4)
    {
        record.vecVoltageDataChannel.resize(array.count()/sizeof(float));
        memcpy( &record.vecVoltageDataChannel[0], array.data(), array.count() );
    }

    array = dbRecord.value(ARRESTER_RESISTIVE_CURRENT_CHANNEL).toByteArray();
    if (array.size() >= 4)
    {
        record.vecResistiveCurrentData.resize(array.count()/sizeof(float));
        memcpy( &record.vecResistiveCurrentData[0], array.data(), array.count() );
    }

    return record;
}

Sf6StateRecord DBServer::getSf6StateRecord(const DataRecord &record) const
{
    Sf6StateRecord rtn(getRecordBase(record));
    rtn.pressure = record.value(SF6_PRESSURE).toFloat();
    rtn.density = record.value(SF6_DENSITY).toFloat();
    rtn.temprature = record.value(SF6_TEMPRETURE).toFloat();
    rtn.envTemprature = record.value(SF6_ENV_TEMPERATURE).toFloat();
    rtn.alarmStatus = record.value(SF6_ALARM_STATUS).toInt();
    rtn.superCapvoltage = record.value(SF6_SUPERCAP_VOLTAGE).toFloat();
    rtn.sf6value = record.value(SF6_SF6).toFloat();
    rtn.o2value = record.value(SF6_O2).toFloat();
    return rtn;
}

EnvStatusRecord DBServer::getEnvStatusRecord(const DataRecord &record) const
{
    EnvStatusRecord rtn(getRecordBase(record));
    rtn.status = static_cast<EnvStatus>(record.value(ENV_STATUS).toInt());
    rtn.value = record.value(ENV_STATUS_VALUE).toFloat();
    return rtn;
}

HkVideoRecord DBServer::getHkVideoRecord(const DataRecord &record) const
{
    HkVideoRecord rtn(getRecordBase(record));
    auto tmplist = QJsonDocument::fromJson(record.value(HK_VIDEO_IMAGE_PATH).toByteArray()).array().toVariantList();
    std::transform(tmplist.cbegin(), tmplist.cend(), std::back_inserter(rtn.imagePath), [](const QVariant& itrValue){return itrValue.toString();});
    rtn.captureType = record.value(HK_CAPTURE_TYPE).toInt();
    rtn.maxTemperature = record.value(HK_MAX_TEMPRATURE).toDouble();
    rtn.avgTemperature = record.value(HK_AVG_TEMPRATURE).toDouble();
    rtn.maxTempraturePostionX = record.value(HK_MAX_TEMPRATURE_POSITION_X).toDouble();
    rtn.maxTempraturePostionY = record.value(HK_MAX_TEMPRATURE_POSITION_Y).toDouble();
    rtn.strRegionInfo = record.value(HK_REGION_INFO).toString();
    return rtn;

}

/*************************************************
功能： 将数据记录转换为振动记录
输入参数：
        dbRecord -- 数据记录集
返回值：
        振动记录
*************************************************************/
VibrationRecord DBServer::getVibrationRecord( const DataRecord& dbRecord ) const
{
    VibrationRecord record;
    record.datetime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.iautoId = dbRecord.value(IK_ID).toInt();
    record.strRecordID = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.bIsUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.stPointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.stPointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.stPointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.stPointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.stPointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.uiBattery = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.usRecordID = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.illGlobalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.ucChannelID = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.strADUID);
    record.usRecordID = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.ucFrequency = dbRecord.value(VIBRATION_FREQUENCY).toInt();
    record.ucSampleCycleCount = dbRecord.value(VIBRATION_SAMPLE_COUNT).toInt();
    record.usSampleCountCycle = dbRecord.value(VIBRATION_SAMPLE_RATE).toInt();

    QByteArray array;

    array = dbRecord.value(VIBRATION_X_ARRAY).toByteArray();
    if (array.size() >= 4)
    {
        record.stVibrationDataX.vecArray.resize(array.count()/sizeof(double));
        memcpy( &record.stVibrationDataX.vecArray[0], array.data(), array.count() );
    }

    array = dbRecord.value(VIBRATION_Y_ARRAY).toByteArray();
    if (array.size() >= 4)
    {
        record.stVibrationDataY.vecArray.resize(array.count()/sizeof(double));
        memcpy( &record.stVibrationDataY.vecArray[0], array.data(), array.count() );
    }

    array = dbRecord.value(VIBRATION_Z_ARRAY).toByteArray();
    if (array.size() >= 4)
    {
        record.stVibrationDataZ.vecArray.resize(array.count()/sizeof(double));
        memcpy( &record.stVibrationDataZ.vecArray[0], array.data(), array.count() );
    }

    record.stVibrationDataX.iAmplitudeMax = dbRecord.value(VIBRATION_X_AMP_MAX).toInt();
    record.stVibrationDataX.iAmplitudeAverage = dbRecord.value(VIBRATION_X_AMP_AVG).toInt();
    record.stVibrationDataX.dAccelerationMax = dbRecord.value(VIBRATION_X_ACC_MAX).toDouble();
    record.stVibrationDataX.dAccelerationAverage = dbRecord.value(VIBRATION_X_ACC_AVG).toDouble();
    array = dbRecord.value(VIBRATION_X_MAX_FREQ).toByteArray();
    if (array.size() >= 4)
    {
        record.stVibrationDataX.vecMaxFreqs.resize(array.count()/sizeof(double));
        memcpy( &record.stVibrationDataX.vecMaxFreqs[0], array.data(), array.count() );
    }

    record.stVibrationDataY.iAmplitudeMax = dbRecord.value(VIBRATION_Y_AMP_MAX).toInt();
    record.stVibrationDataY.iAmplitudeAverage = dbRecord.value(VIBRATION_Y_AMP_AVG).toInt();
    record.stVibrationDataY.dAccelerationMax = dbRecord.value(VIBRATION_Y_ACC_MAX).toDouble();
    record.stVibrationDataY.dAccelerationAverage = dbRecord.value(VIBRATION_Y_ACC_AVG).toDouble();
    array = dbRecord.value(VIBRATION_Y_MAX_FREQ).toByteArray();
    if (array.size() >= 4)
    {
        record.stVibrationDataY.vecMaxFreqs.resize(array.count()/sizeof(double));
        memcpy( &record.stVibrationDataY.vecMaxFreqs[0], array.data(), array.count() );
    }

    record.stVibrationDataZ.iAmplitudeMax = dbRecord.value(VIBRATION_Z_AMP_MAX).toInt();
    record.stVibrationDataZ.iAmplitudeAverage = dbRecord.value(VIBRATION_Z_AMP_AVG).toInt();
    record.stVibrationDataZ.dAccelerationMax = dbRecord.value(VIBRATION_Z_ACC_MAX).toDouble();
    record.stVibrationDataZ.dAccelerationAverage = dbRecord.value(VIBRATION_Z_ACC_AVG).toDouble();
    array = dbRecord.value(VIBRATION_Z_MAX_FREQ).toByteArray();
    if (array.size() >= 4)
    {
        record.stVibrationDataZ.vecMaxFreqs.resize(array.count()/sizeof(double));
        memcpy( &record.stVibrationDataZ.vecMaxFreqs[0], array.data(), array.count() );
    }

    return record;
}

QVector<VibrationRecord> DBServer::getVibrationScanData(const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    return transformSequence<QVector<VibrationRecord> >(getDataByScanTime(DB_VIBRATION, pointId, start, end),\
                                                        [this](const DataRecord& record){return getVibrationRecord(record);});
}

FrostRawRecord DBServer::getFrostRawRecord( const DataRecord& dbRecord )
{
    FrostRawRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(FROST_RAW_SENSOR_TYPE).toInt());
    record.fFrostPointRaw = dbRecord.value(FROST_RAW_DATA).toFloat();

    return record;
}
FrostAtmRecord DBServer::getFrostAtmRecord( const DataRecord& dbRecord )
{
    FrostAtmRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(FROST_ATM_SENSOR_TYPE).toInt());
    record.fFrostPointAtm = dbRecord.value(FROST_ATM_DATA).toFloat();

    return record;
}
DewRawRecord DBServer::getDewRawRecord( const DataRecord& dbRecord )
{
    DewRawRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(DEW_RAW_SENSOR_TYPE).toInt());
    record.fDewPointRaw = dbRecord.value(DEW_RAW_DATA).toFloat();

    return record;
}
DewAtmRecord DBServer::getDewAtmRecord( const DataRecord& dbRecord )
{
    DewAtmRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(DEW_ATM_SENSOR_TYPE).toInt());
    record.fDewPointAtm = dbRecord.value(DEW_ATM_DATA).toFloat();

    return record;
}
MoistureRecord DBServer::getMoistureRecord( const DataRecord& dbRecord )
{
    MoistureRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(MOISTURE_SENSOR_TYPE).toInt());
    record.fMoisture = dbRecord.value(MOISTURE_DATA).toFloat();

    return record;
}
PressAbsoRecord DBServer::getPressAbsoRecord( const DataRecord& dbRecord )
{
    PressAbsoRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(PRESS_ABSO_SENSOR_TYPE).toInt());
    record.fPressAbsolute = dbRecord.value(PRESS_ABSO_DATA).toFloat();

    return record;
}
PressNormRecord DBServer::getPressNormRecord( const DataRecord& dbRecord )
{
    PressNormRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(PRESS_NORM_SENSOR_TYPE).toInt());
    record.fPressNormal = dbRecord.value(PRESS_NORM_DATA).toFloat();

    return record;
}
DensityRecord DBServer::getDensityRecord( const DataRecord& dbRecord )
{
    DensityRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(DENSITY_SENSOR_TYPE).toInt());
    record.fDensity = dbRecord.value(DENSITY_DATA).toFloat();

    return record;
}
OxygenRecord DBServer::getOxygenRecord( const DataRecord& dbRecord )
{
    OxygenRecord record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(OXYGEN_SENSOR_TYPE).toInt());
    record.fOxygen = dbRecord.value(OXYGEN_DATA).toFloat();

    return record;
}
SF6Record DBServer::getSF6Record( const DataRecord& dbRecord )
{
    SF6Record record;
    record.recordTime = dbRecord.value(IK_RECORDTIME).toDateTime();
    record.autoId = dbRecord.value(IK_ID).toInt();
    record.recordStringId = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();
    record.isUpdate = dbRecord.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    record.pointArchiveInfo.strDeviceGUID = dbRecord.value(SAMPLE_DATA_DEVICE_ID).toString();
    record.pointArchiveInfo.strDevicePMS = dbRecord.value(SAMPLE_DATA_DEVICE_PMS).toString();
    record.pointArchiveInfo.strPointGUID = dbRecord.value(SAMPLE_DATA_POINT_GUID).toString();
    record.pointArchiveInfo.strStationGUID = dbRecord.value(SAMPLE_DATA_STATION_ID).toString();
    record.pointArchiveInfo.strStationPMS = dbRecord.value(SAMPLE_DATA_STATION_PMS).toString();
    record.batteryInfo = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    record.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    record.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    record.recordId = dbRecord.value(SAMPLE_DATA_RECORD_ID).toInt();
    record.globalId = dbRecord.value(IK_GLOBAL_ID).toLongLong();
    record.channelId = getChannelInfo(dbRecord.value(SAMPLE_DATA_CHANNEL_NAME).toString(),record.aduId);

    record.sensorType = ADUType(dbRecord.value(SF6_SENSOR_TYPE).toInt());
    record.fSF6 = dbRecord.value(SF6_DATA).toFloat();

    return record;
}

bool DBServer::getPDSMPDRecord( const DataRecord& dbRecord, PDSMPDATA &stPDSMPDATA)
{
    bool bRet = false;
    stPDSMPDATA.daqtime = dbRecord.value(IK_RECORDTIME).toDateTime();
    stPDSMPDATA.stBattery.battery = dbRecord.value(SAMPLE_DATA_BATTERY).toInt();
    stPDSMPDATA.stSignlInfo.signalstrength = dbRecord.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    stPDSMPDATA.stSignlInfo.noiseratio = dbRecord.value(SAMPLE_NOISE_RATIO).toInt();
    stPDSMPDATA.dataID = dbRecord.value(SAMPLE_DATA_DATA_ID).toString();

    if ( stPDSMPDATA.daqtime.isValid() )
    {
        bRet = true;
    }
    switch( stPDSMPDATA.lnType )
    {
    case MMXU:
    {
        METERDATA mtData;
        mtData.aA[0] = dbRecord.value(METER_DATA_CURRENT_A).toFloat();
        mtData.phvA[0] = dbRecord.value(METER_DATA_VOLTAGE_A).toFloat();
        mtData.totW = dbRecord.value(METER_DATA_TOTAL_POWER).toFloat();
        mtData.totVar = dbRecord.value(METER_DATA_TOTAL_VAR).toFloat();
        stPDSMPDATA.data = QVariant::fromValue(mtData);
    }
        break;
    case LVMETER:
    {
        LVMETERDATA lvData;
        lvData.data.aA[0] = dbRecord.value(LVMETER_DATA_CURRENT_A).toFloat();
        lvData.data.phvA[0] = dbRecord.value(LVMETER_DATA_VOLTAGE_A).toFloat();
        lvData.data.ppvAB[0] = dbRecord.value(LVMETER_DATA_VOLTAGE_LINE_A).toFloat();
        lvData.data.totW = dbRecord.value(LVMETER_DATA_TOTAL_POWER).toFloat();
        lvData.data.totVar = dbRecord.value(LVMETER_DATA_TOTAL_VAR).toFloat();
        lvData.bPwr1 = dbRecord.value(LVMETER_DATA_SWITCH_STATE).toBool();
        stPDSMPDATA.data = QVariant::fromValue(lvData);
    }
        break;
    case CACC:
    {
        CACCDATA airData;
        airData.bOn = dbRecord.value(CACC_DATA_SWITCH_STATE).toBool();
        airData.eMode = (CACCMode)dbRecord.value(CACC_DATA_WORK_MODE).toInt();
        airData.fTmp = dbRecord.value(CACC_DATA_TEMP).toFloat();
        airData.nFan = dbRecord.value(CACC_DATA_FAN).toInt();
        stPDSMPDATA.data = QVariant::fromValue(airData);
    }
        break;
    case SOUND:
    {
        SOUNDDATA sndData;
        sndData.fSnd = dbRecord.value(ENV_STATUS_VALUE).toFloat();
        stPDSMPDATA.data = QVariant::fromValue(sndData);
    }
        break;
    case SMOKE:
    {
        SMOKEDATA smkData;
        smkData.bAlarm = dbRecord.value(ENV_STATUS).toBool();
        stPDSMPDATA.data = QVariant::fromValue(smkData);
    }
        break;
    case SWLA:
    {
        SWLADATA swlaData;
        swlaData.bAlarm = dbRecord.value(ENV_STATUS).toBool();
        stPDSMPDATA.data = QVariant::fromValue(swlaData);
    }
        break;
    case WATER:
    {
        WATERLEVELDATA waterData;
        waterData.fLevel = dbRecord.value(WATER_LEVEL_DATA_ALARM).toFloat();
        stPDSMPDATA.data = QVariant::fromValue(waterData);
    }
        break;
    case CLSL:
    case KFAN:
    {
        SWITCHDATA switchData;
        switchData.bOn = dbRecord.value(LOW_TENSION_SWITCH_DATA_SWITCH_STATE).toBool();
        stPDSMPDATA.data = QVariant::fromValue(switchData);
    }
        break;
    case FIBER:
    {
        QByteArray arrayData = dbRecord.value(FIBER_TEMP_DATA_ARRAY).toByteArray();
        QVector<float> vecFiberData;
        int dataSize = arrayData.size();
        if ( 0 != dataSize )
        {
            vecFiberData.resize( dataSize / sizeof( float ) );
            memcpy( &vecFiberData[0], arrayData.data(), arrayData.size() );
        }
        QVector<STMPDATA> vecTmp;
        for (int i = 0; i < vecFiberData.size(); i++)
        {
            STMPDATA stSTMPDATA;
            stSTMPDATA.fTmp = vecFiberData.at(i);
            vecTmp.append(stSTMPDATA);
        }
        stPDSMPDATA.data = QVariant::fromValue(vecTmp);
    }
        break;
    case CAMERA:
    {
        HkVideoRecord videoRecord = getHkVideoRecord(dbRecord);
        stPDSMPDATA.data.setValue(videoRecord);
    }
        break;
    default:
        break;
    }

    return bRet;
}

QVector< ::AERecord> DBServer::getAeDataByIndex(const QString& pointId, qint64 dataindexstart, qint64 dataindexend) const
{
    PDS_SYS_INFO_LOG("DBServer::getAeDataByIndex start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getAeDataByIndex end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordByIndex(pointId, dataindexstart, dataindexend, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::AERecord>>(tmpdatas, [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdAeData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdAeData(pointId, *unwrapData) : ::AERecord{};
    });
}

QVector< ::TEVRecord> DBServer::getTevDataByIndex(const QString& pointId, qint64 dataindexstart, qint64 dataindexend) const
{
    PDS_SYS_INFO_LOG("DBServer::getTevDataByIndex start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getTevDataByIndex end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordByIndex(pointId, dataindexstart, dataindexend, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::TEVRecord>>(tmpdatas, [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdTevData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdTevData(pointId, *unwrapData) : ::TEVRecord{};
    });
}

QVector< ::PRPSRecord> DBServer::getUhfDataByIndex(const QString &pointId, qint64 dataindexstart, qint64 dataindexend) const
{
    PDS_SYS_INFO_LOG("DBServer::getUhfDataByIndex start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getUhfDataByIndex end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordByIndex(pointId, dataindexstart, dataindexend, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::PRPSRecord>>(tmpdatas, [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdUhfData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdUhfData(pointId, *unwrapData) : ::PRPSRecord{};
    });
}

QVector< ::PRPSRecord> DBServer::getHfctDataByIndex(const QString &pointId, qint64 dataindexstart, qint64 dataindexend) const
{
    PDS_SYS_INFO_LOG("DBServer::getHfctDataByIndex start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getHfctDataByIndex end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordByIndex(pointId, dataindexstart, dataindexend, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::PRPSRecord>>(tmpdatas, [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdHfctData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdHfctData(pointId, *unwrapData) : ::PRPSRecord{};
    });
}

QVector<MechDefine::MechISRecord> DBServer::getMechDataByIndex(const QString &pointId, qint64 dataindexstart, qint64 dataindexend) const
{
    return transformSequence<QVector<MechDefine::MechISRecord>>(getDataByIndex(DB_MECH, pointId, dataindexstart, dataindexend),
                                                                [this](const DataRecord& input){return getMECHRecord(input);});
}

QVector< ::TEMPRecord> DBServer::getTempDataByIndex(const QString& pointId, qint64 dataindexstart, qint64 dataindexend) const
{
    return transformSequence<QVector<TEMPRecord>>(getDataByIndex(DB_TEMP, pointId, dataindexstart, dataindexend),
                                                              [this](const DataRecord& input){return getTEMPRecord(input);});
}

QVector< ::Sf6StateRecord> DBServer::getSf6StateDataByIndex(const QString& pointId, qint64 dataindexstart, qint64 dataindexend) const
{
    return transformSequence<QVector<Sf6StateRecord>>(getDataByIndex(DB_SF6_STATE, pointId, dataindexstart, dataindexend),
                                                              [this](const DataRecord& input){return getSf6StateRecord(input);});
}

QVector< ::EnvStatusRecord> DBServer::getWaterloggingDataByIndex(const QString& pointId, qint64 dataindexstart, qint64 dataindexend) const
{
    return transformSequence<QVector<EnvStatusRecord>>(getDataByIndex(DB_ENV_STATUS, pointId, dataindexstart, dataindexend),
                                                          [this](const DataRecord& input){return getEnvStatusRecord(input);});
}

QVector<z058::transfer::WirelessData> DBServer::getWirelessData(const QString &pointId, qint64 dataindexstart, qint64 dataindexend) const
{
    if (!m_configService)
    {
        PDS_SYS_ERR_LOG("m_configService is null");
        return {};
    }
    QList<ADUChannelType> aduchanneltypes;
    if (!((aduchanneltypes = m_configService->getChannelTypeListFromPoint(pointId)).size() > 0))
    {
        PDS_SYS_ERR_LOG("get adutype failed!");
        return {};
    }
    QVector<z058::transfer::WirelessData> rtn;
    auto aduchanneltype = aduchanneltypes.front();
    switch (aduchanneltype)
    {
    case CHANNEL_AE:
        return transformSequence<QVector<z058::transfer::WirelessData>>(getAeDataByIndex(pointId, dataindexstart, dataindexend),
                                                                        [](const ::AERecord& input)
        {return storage::DataTypeConvertionUtils::convertToWirelessData(input);});
        break;
    case CHANNEL_TEV:
        rtn = transformSequence<QVector<z058::transfer::WirelessData>>(getTevDataByIndex(pointId, dataindexstart, dataindexend),
                                                                       [](const ::TEVRecord& input)
        {return storage::DataTypeConvertionUtils::convertToWirelessData(input);});
        break;
    case CHANNEL_UHF:
        rtn = transformSequence<QVector<z058::transfer::WirelessData>>(getUhfDataByIndex(pointId, dataindexstart, dataindexend),
                                                                              [](const ::PRPSRecord& input)
        {return storage::DataTypeConvertionUtils::convertToWirelessData(input);});
        break;
    case CHANNEL_HFCT:
        rtn = transformSequence<QVector<z058::transfer::WirelessData>>(getHfctDataByIndex(pointId, dataindexstart, dataindexend),
                                                                    [](const ::PRPSRecord& input)
        {return storage::DataTypeConvertionUtils::convertToWirelessData(input, false);});
        break;
    case CHANNEL_MECH:
        rtn = transformSequence<QVector<z058::transfer::WirelessData>>(getMechDataByIndex(pointId, dataindexstart, dataindexend),
                                                                    [](const MechDefine::MechISRecord& input)
        {return storage::DataTypeConvertionUtils::convertToWirelessData(input);});
        break;
    case CHANNEL_TEMPERATURE:
        rtn = transformSequence<QVector<z058::transfer::WirelessData>>(getTempDataByIndex(pointId, dataindexstart, dataindexend),
                                                                    [](const ::TEMPRecord& input)
        {return storage::DataTypeConvertionUtils::convertToWirelessData(input);});
        break;
    case CHANNEL_SF6_GAS:
        rtn = transformSequence<QVector<z058::transfer::WirelessData>>(getSf6StateDataByIndex(pointId, dataindexstart, dataindexend),
                                                                    [](const ::Sf6StateRecord& input)
        {return storage::DataTypeConvertionUtils::convertToWirelessData(input);});
        break;
    case CHANNEL_FLOOD:
        rtn = transformSequence<QVector<z058::transfer::WirelessData>>(getWaterloggingDataByIndex(pointId, dataindexstart, dataindexend),
                                                                    [](const ::EnvStatusRecord& input)
        {return storage::DataTypeConvertionUtils::convertToWirelessData(input);});
        break;
    default:
        break;
    }
    return rtn;
}



bool DBServer::getAeRecordGlobalIdAndAutoIdByDateTime(const QString &pointId, const QDateTime &moment, quint64 *globalId, quint64 *autoId)
{
    auto status = m_storageengine->getAutoIndexAndGlobalIndex(pointId, moment, autoId, globalId);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return false;
    }
    return true;
}

bool DBServer::getTevRecordGlobalIdAndAutoIdByDateTime(const QString &pointId, const QDateTime &moment, quint64 *globalId, quint64 *autoId)
{
    auto status = m_storageengine->getAutoIndexAndGlobalIndex(pointId, moment, autoId, globalId);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return false;
    }
    return true;
}

bool DBServer::getUhfRecordGlobalIdAndAutoIdByDateTime(const QString &pointId, const QDateTime &moment, quint64 *globalId, quint64 *autoId)
{
    auto status = m_storageengine->getAutoIndexAndGlobalIndex(pointId, moment, autoId, globalId);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return false;
    }
    return true;
}

bool DBServer::getHfctRecordGlobalIdAndAutoIdByDateTime(const QString &pointId, const QDateTime &moment, quint64 *globalId, quint64 *autoId)
{
    auto status = m_storageengine->getAutoIndexAndGlobalIndex(pointId, moment, autoId, globalId);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return false;
    }
    return true;
}

bool DBServer::getTempRecordGlobalIdAndAutoIdByDateTime(const QString &pointId, const QDateTime &moment, quint64 *globalId, quint64 *autoId)
{
    return getSqliteRecordGlobalIdAndAutoIdByDateTime(DB_TEMP, pointId, moment, globalId, autoId);
}

bool DBServer::getHumaRecordGlobalIdAndAutoIdByDateTime(const QString &pointId, const QDateTime &moment, quint64 *globalId, quint64 *autoId)
{
    return getSqliteRecordGlobalIdAndAutoIdByDateTime(DB_HUMI, pointId, moment, globalId, autoId);
}

QVector<AERecord> DBServer::getAeDataBeforeMoment(const QString &pointId, const QDateTime &moment, qint64 recordsize) const
{
    PDS_SYS_INFO_LOG("DBServer::getAeScanData start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getAeScanData end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordBeforeTimeMoment(pointId, moment, recordsize, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::AERecord>>(tmpdatas,
                                                   [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdAeData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdAeData(pointId, *unwrapData) : ::AERecord{};
    });
}

QVector<TEVRecord> DBServer::getTevDataBeforeMoment(const QString &pointId, const QDateTime &moment, qint64 recordsize) const
{
    PDS_SYS_INFO_LOG("DBServer::getTEVRecord start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getTEVRecord end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordBeforeTimeMoment(pointId, moment, recordsize, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::TEVRecord>>(tmpdatas,
                                                    [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdTevData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdTevData(pointId, *unwrapData) : ::TEVRecord{};
    });
}

QVector<PRPSRecord> DBServer::getUhfDataBeforeMoment(const QString &pointId, const QDateTime &moment, qint64 recordsize) const
{
    PDS_SYS_INFO_LOG("DBServer::getUhfScanData start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getUhfScanData end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordBeforeTimeMoment(pointId, moment, recordsize, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::PRPSRecord>>(tmpdatas,
                                                     [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdUhfData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdUhfData(pointId, *unwrapData) : ::PRPSRecord{};
    });
}

QVector<PRPSRecord> DBServer::getHfctDataBeforeMoment(const QString &pointId, const QDateTime &moment, qint64 recordsize) const
{
    PDS_SYS_INFO_LOG("DBServer::getHfctDataByIndex start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getHfctDataByIndex end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordBeforeTimeMoment(pointId, moment, recordsize, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::PRPSRecord>>(tmpdatas,
                                                     [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdHfctData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdHfctData(pointId, *unwrapData) : ::PRPSRecord{};
    });
}

QVector<PRPSRecord> DBServer::getTevPrpsDataBeforeMoment(const QString &pointId, const QDateTime &moment, qint64 recordsize) const
{
    PDS_SYS_INFO_LOG("DBServer::getTevPrpsDataBeforeMoment start");
    ExitGuard exitGuard([]{PDS_SYS_INFO_LOG("DBServer::getTevPrpsDataBeforeMoment end");});

    QVector<common::DataWrapper> tmpdatas;
    auto status = m_storageengine->getRecordBeforeTimeMoment(pointId, moment, recordsize, &tmpdatas);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return {};
    }

    return transformSequence<QVector< ::PRPSRecord>>(tmpdatas,
                                                     [this, &pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdTEVPRPSData>();
        return unwrapData ? DataTypeConvertionUtils::convertFromPdTEVPRPSData(pointId, *unwrapData) : ::PRPSRecord{};
    });
}

QVector<TEMPRecord> DBServer::getTempDataBeforeMoment(const QString &pointId, const QDateTime &moment, qint64 recordsize) const
{
    return transformSequence<QVector<TEMPRecord>>(getDataBeforeMoment(DB_TEMP, pointId, moment, recordsize),
                                                              [this](const DataRecord& input){return getTEMPRecord(input);});
}

QVector<HumidityRecord> DBServer::getHumanityDataBeforeMoment(const QString &pointId, const QDateTime &moment, qint64 recordsize) const
{
    return transformSequence<QVector<HumidityRecord>>(getDataBeforeMoment(DB_HUMI, pointId, moment, recordsize),
                                                              [this](const DataRecord& input){return getHumidityRecord(input);});
}

/*************************************************
功能： 根据通道名称获取前端id与通道索引
输入参数：
        strChannelName -- 通道名称
输出参数：
        strADUID -- 前端id
返回值：
        通道索引
*************************************************************/
int DBServer::getChannelInfo(const QString &strChannelName, QString &strADUID) const
{
    int ChannelIndex = 0;
    if (!strChannelName.isEmpty())
    {
        QStringList listChannelName;
        listChannelName = strChannelName.split("_");
        strADUID = listChannelName.at(1);
        ChannelIndex = listChannelName.at(3).toInt();
    }
    return ChannelIndex;
}

/*************************************************
功能： 添加一条报警数据
输入参数：
        record -- 数据记录数据结构
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::addAlarmRecord( const AlarmRecord &record)
{
    DataRecord dbRecord;
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_ALARM];

    dbRecord.setData( ALARM_ID, pdb->updateGlobalID(pdb->getGlobalID(ALARM_GLOBAL_ID_START) ));
    dbRecord.setData( ALARM_DATA_ID, record.ullDataID );
    dbRecord.setData( IK_RECORDTIME, record.dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( ALARM_CHANNELNAME, record.strChannelName );
    dbRecord.setData( ALARM_MEU, record.strMEUID );
    dbRecord.setData( ALARM_CHANNELID, record.usChannelID );
    dbRecord.setData( ALARM_THRE_STATE, record.eThreState );
    dbRecord.setData( ALARM_DIFF_STATE, record.eDiffState );
    dbRecord.setData( ALARM_TYPE, record.usAlarmType );

    dbRecord.setData( ALARM_CHANNEL_TYPE, record.eType );//制表用

    return pdb->addRecord( dbRecord );
}
bool DBServer::addAlarmRecord( const DeviceAlarmRecord &record)
{
    DataRecord dbRecord;
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_ALARM];

    dbRecord.setData( DEVICE_ALARM_ID, pdb->updateGlobalID(pdb->getGlobalID(ALARM_GLOBAL_ID_START) ));
    dbRecord.setData( IK_RECORDTIME, record.dateTime.toString(DATETIME_FORMAT) );
    dbRecord.setData( DEVICE_ALARM_DEVICE_ID, record.usDeviceID );
    dbRecord.setData( DEVICE_ALARM_CONFIDENCE_LEVEL, record.ucConfidencetLevel );
    dbRecord.setData( DEVICE_ALARM_TYPE, record.usAlarmType );
    dbRecord.setData( DEVICE_ALARM_DATA, record.strAlarmData );
    dbRecord.setData( DEVICE_ALARM_STATE, 0 );
    dbRecord.setData( DEVICE_ALARM_ADU_TYPE, record.eMonitorType );

    return pdb->addRecord( dbRecord , DEVICE_ALARM_TABLE );
}

bool DBServer::addAlarmRecord( const SF6AlarmRecord &record )
{
    DataRecord dbRecord;
    SqliteDB* pdb = (SqliteDB*)m_mapDBs[DB_ALARM];

    dbRecord.setData( SF6_ALARM_DEVICE, record.strAlarmDeviceID );
    dbRecord.setData( SF6_ALARM_POINT, record.strAlarmPointID );
    dbRecord.setData( SF6_ALARM_ADU_ID, record.strAlarmADUID );
    dbRecord.setData( SF6_ALARM_ADU_TYPE, record.eAlarmADUType );
    dbRecord.setData( SF6_ALARM_ADU_CHANNEL, record.eAlarmChannelType );
    dbRecord.setData( SF6_ALARM_THRESHOLD_DATA, record.fAlarmThreshold );
    dbRecord.setData( SF6_ALARM_DATA, record.fAlarmData );
    dbRecord.setData( SF6_ALARM_TIME, record.AlarmDateTime );

    return pdb->addRecord( dbRecord , SF6_ALARM_TABLE );
}

bool DBServer::addAlarmRecord( const AduAlarmRecord &record )
{
    emit sigAddAduAlarmDataToDB(record);

    return true;
}

bool DBServer::getAlarmRecord(QList<AduAlarmRecord> &recordList, QString selectAduID)
{
    AlarmDB* pdb = qobject_cast<AlarmDB*>(m_mapDBs[DB_ALARM]);
    return pdb->getAduAlarmRecord(recordList, selectAduID);
}

/*************************************************
功能： 获取某设备当天是否有某类型的报警
输入参数：
        usDeviceID -- 设备id
        eADUType -- 前端类型
返回值：
        true -- 有
        else -- 没有
*************************************************************/
bool DBServer::isTodayExistedDeviceAlarm( quint16 usDeviceID, MonitorType eMonitorType )
{
    AlarmDB* pdb = (AlarmDB*)m_mapDBs[DB_ALARM];
    return pdb->isTodayExistedDeviceAlarm( usDeviceID, eMonitorType );
}

/*************************************************
功能： 获取是否存在该条传感器告警记录
输入参数：
        strAduID            -- 传感器id
        qui64RecordTimeT    -- 告警时间戳
        qui16DataID         -- 告警数据ID
返回值：
        true -- 有
        else -- 没有
*************************************************************/
bool DBServer::hasAduAlarmRecord(const QString &strAduID, quint64 qui64RecordTimeT, quint16 qui16DataID)
{   
    bool bHasRecord = false;

    AlarmDB* pdb = qobject_cast<AlarmDB*>(m_mapDBs[DB_ALARM]);
    if(pdb)
    {
       bHasRecord =  pdb->hasAduAlarmRecord(strAduID, qui64RecordTimeT, qui16DataID);
    }

    return bHasRecord;
}

/*************************************************
功能： 获取某设备当天是否有某类型的未确认报警
输入参数：
        usDeviceID -- 设备id
        eADUType -- 前端类型
返回值：
        true -- 有
        else -- 没有
*************************************************************/
bool DBServer::isTodayDeviceAlarmConfirmed(quint16 usDeviceID, MonitorType eMonitorType)
{
    AlarmDB* pdb = (AlarmDB*)m_mapDBs[DB_ALARM];
    return pdb->isTodayDeviceAlarmConfirmed( usDeviceID, eMonitorType );
}

/*************************************************
功能： 更新某设备当天的报警记录
输入参数：
        record -- 设备报警数据结构体
返回值：
        操作结果
*************************************************************/
bool DBServer::updateAlarmRecord( const DeviceAlarmRecord &record )
{
    AlarmDB* pdb = (AlarmDB*)m_mapDBs[DB_ALARM];
    return pdb->updateAlarmRecord( record );
}

/*************************************************
功能： 获取某设备报警数量
输入参数：
        usDeviceID -- 设备ID
        bIsRecordConfirmed -- 报警数据是否确认
返回值：
        某设备报警数量
*************************************************************/
quint64 DBServer::getDeviceAlarmRecordCount(quint16 usDeviceID, bool bIsRecordConfirmed )
{
    AlarmDB* pdb = (AlarmDB*)m_mapDBs[DB_ALARM];
    return pdb->getDeviceAlarmRecordCount( usDeviceID, bIsRecordConfirmed );
}

/*************************************************
功能： 将QByteArray转换为QVector数组
输入参数：
        dataRecord -- QByteArray
返回值：
        QVector数组
*************************************************************/
const QVector<double> DBServer::getQVectordoubleFromdata(const QByteArray &dataRecord)
{
    int count = dataRecord.count()/sizeof(double)*sizeof(char);
    double adRecord[count];
    QVector<double> dRecordList;
    memcpy(adRecord,dataRecord.data(),dataRecord.count());
    for (int i = 0; i < count; i++)
    {
        dRecordList.append(adRecord[i]);
    }

    return dRecordList;
}
const QVector<quint8> DBServer::getQVectorquint8Fromdata(const QByteArray &dataRecord)
{
    int count = dataRecord.count()/sizeof(quint8)*sizeof(char);
    double adRecord[count];
    QVector<quint8> ucRecordList;
    memcpy(adRecord,dataRecord.data(),dataRecord.count());
    for (int i = 0; i < count; i++)
    {
        ucRecordList.append(adRecord[i]);
    }

    return ucRecordList;
}

/*************************************************
函数名： exportdb
输入参数： dblist -- 需要导出的数据库的类型
        timeStart -- 开始时间
        timeEnd -- 结束时间
输出参数： NULL
返回值： 实例对象
功能： 获取实例
*************************************************************/
DBExportErrorCode DBServer::exportDB(const QString &exportpath, const QList<int> & dbList, const QDateTime &timeStart, const QDateTime &timeEnd)
{
    if (exportpath == "")
    {
        return DB_EXPORT_FILE_PATH_ERROR;
    }
    if((timeStart >= timeEnd) || (timeEnd >QDateTime::currentDateTime()))
    {
        return DB_EXPORT_TIME_ERROR;
    }

    //创建导出路径
    QString strTimeStart = timeStart.toString("yyyyMMddhhmmss");
    QString strTimeEnd = timeEnd.toString("yyyyMMddhhmmss");
    QString exportPath = exportpath + "/DBexport_"+strTimeStart+"_"+strTimeEnd;
    QDir dataBaseDir(exportPath);
    if (!dataBaseDir.exists())
    {
        if(!dataBaseDir.mkpath(exportPath))
        {
            return DB_EXPORT_FILE_PATH_ERROR;
        }
    }

    DBExportErrorCode eDBExoprtResult = DB_EXPORT_NO_DATE;
    for (int i = 0; i < dbList.size(); i++)
    {
        if (i > DB_COUNT)
        {
            break;
        }
        if ( dbList.at(i) == 1)
        {
            SqliteDB* pdb = (SqliteDB*)m_mapDBs[i];
            eDBExoprtResult = pdb->exportDB(exportPath, timeStart, timeEnd );
            if (eDBExoprtResult != DB_EXPORT_SUCCESSED)
            {
                if (eDBExoprtResult == DB_EXPORT_USB_DRIVER_FULL)
                {
                    return eDBExoprtResult;
                }
                eDBExoprtResult = DB_EXPORT_NO_DATE;
            }
        }
    }

    QFile::copy(QDir::currentPath()+CONFIG_FILE_NAME, exportPath+CONFIG_FILE_NAME);
#ifdef Q_WS_QWS
    system("sync");
#endif
    return eDBExoprtResult;
}


/*************************************************
函数名： exportdb
输入参数： dblist -- 需要导出的数据库的类型
        timeStart -- 开始时间
        timeEnd -- 结束时间
输出参数： NULL
返回值： 实例对象
功能： 获取实例
*************************************************************/
DBExportErrorCode DBServer::exportDB(QString &exportpath, const QDate &timeStart, const QDate &timeEnd)
{
    if (exportpath == "")
    {
        return DB_EXPORT_FILE_PATH_ERROR;
    }
    if((timeStart > timeEnd) || (timeEnd > QDate::currentDate()))
    {
        return DB_EXPORT_TIME_ERROR;
    }

    //获取站点名，设备唯一识别码来创建导出路径
    QString strTimeStart = timeStart.toString("yyyyMMdd");
    QString strTimeEnd = timeEnd.toString("yyyyMMdd");
    QString strStationName = "";
    if (!m_configService->getStationID(strStationName))
    {
        return DB_EXPORT_NO_STATION_CONFIG;
    }
    QString strMonitorID = m_webserverCommand->monitorID();
    exportpath = exportpath + QDir::separator() + strStationName + QDir::separator() + strMonitorID + "/DBexport_" + strTimeStart + "_" + strTimeEnd;
    QDir dataBaseDir(exportpath);
    if (!dataBaseDir.exists())
    {
        if(!dataBaseDir.mkpath(exportpath))
        {
            return DB_EXPORT_FILE_PATH_ERROR;
        }
    }

    DBExportErrorCode m_eDBExoprtResult = DB_EXPORT_NO_DATE;
    for (int i = 0; i < DB_COUNT; i++)
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[i];
        DBExportErrorCode eDBExoprtResult = pdb->exportDB(exportpath, (QDateTime)timeStart, (QDateTime)timeEnd );
        if (eDBExoprtResult != DB_EXPORT_SUCCESSED)
        {
            if (eDBExoprtResult == DB_EXPORT_USB_DRIVER_FULL)
            {
                return eDBExoprtResult;
            }
        }
        else
        {
            m_eDBExoprtResult = eDBExoprtResult;
        }
    }

    QFile::remove(exportpath+CONFIG_FILE_NAME);
    QFile::copy(QDir::currentPath()+CONFIG_FILE_NAME, exportpath+CONFIG_FILE_NAME);
#ifdef Q_WS_QWS
    system("sync");
#endif
    return m_eDBExoprtResult;
}

/*************************************************
函数名： exportIncrementData
输入参数： exportpath -- 导出路径
输出参数： NULL
返回值： 操作结果
功能： 导出数据增量
*************************************************************/
DBExportErrorCode DBServer::exportIncrementData(QString &exportpath)
{
    if (exportpath == "")
    {
        return DB_EXPORT_FILE_PATH_ERROR;
    }

    //获取站点名，设备唯一识别码来创建导出路径
    QString strStationName = "";
    if (!m_configService->getStationID(strStationName))
    {
        return DB_EXPORT_NO_STATION_CONFIG;
    }
    QString strMonitorID = m_webserverCommand->monitorID();
    exportpath = exportpath + QDir::separator() + strStationName + QDir::separator() + strMonitorID + "/database";

    QDir dataBaseDir(exportpath);
    if (!dataBaseDir.exists())
    {
        if(!dataBaseDir.mkpath(exportpath))
        {
            return DB_EXPORT_FILE_PATH_ERROR;
        }
    }
    DBExportErrorCode m_eDBExoprtResult = DB_EXPORT_NO_DATE;
    for (int i = 0; i < DB_COUNT; i++)
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[i];
        DBExportErrorCode eDBExoprtResult = pdb->exportIncrementData(exportpath);
        if (eDBExoprtResult != DB_EXPORT_SUCCESSED)
        {
            if (eDBExoprtResult == DB_EXPORT_USB_DRIVER_FULL)
            {
                return eDBExoprtResult;
            }
        }
        else
        {
            m_eDBExoprtResult = eDBExoprtResult;
        }
    }

    QFile::copy(QDir::currentPath()+CONFIG_FILE_NAME, exportpath+CONFIG_FILE_NAME);
#ifdef Q_WS_QWS
    system("sync");
#endif
    return m_eDBExoprtResult;
}

/*************************************************
函数名： exportAllData
输入参数： exportpath -- 导出路径
输出参数： NULL
返回值： 操作结果
功能： 导出全部数据
*************************************************************/
DBExportErrorCode DBServer::exportAllData(QString &exportpath)
{
    if (exportpath == "")
    {
        return DB_EXPORT_FILE_PATH_ERROR;
    }

    //获取站点名，设备唯一识别码来创建导出路径
    QString strStationName = "";
    if (!m_configService->getStationID(strStationName))
    {
        return DB_EXPORT_NO_STATION_CONFIG;
    }
    QString strMonitorID = m_webserverCommand->monitorID();
    exportpath = exportpath + QDir::separator() + strStationName + QDir::separator() + strMonitorID + "/database";

    QDir dataBaseDir(exportpath);
    if (!dataBaseDir.exists())
    {
        if(!dataBaseDir.mkpath(exportpath))
        {
            return DB_EXPORT_FILE_PATH_ERROR;
        }
    }

    DBExportErrorCode eDBExoprtResult = DB_EXPORT_NO_DATE;
    for (int i = 0; i < DB_COUNT; i++)
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[i];
        eDBExoprtResult = pdb->exportAllData(exportpath);
    }

    QFile::copy(QDir::currentPath()+CONFIG_FILE_NAME, exportpath+CONFIG_FILE_NAME);
#ifdef Q_WS_QWS
    system("sync");
#endif
    return eDBExoprtResult;
}

/*************************************************
函数名： exportData
输入参数： strPointName -- 测点名
        eChannelType -- 数据类型
        listAutoIDBegin ListAutoIDEnd -- 需要导出的数据起止ID
输出参数： NULL
返回值： 操作结果
功能： 导出数据
*************************************************************/
bool DBServer::exportData(const QString &strPointName, ADUChannelType eChannelType, const QList<int> &listAutoIDBegin, const QList<int> &ListAutoIDEnd)
{
    SqliteDB* pdb;
    // 删除临时目录的所有文件
    QDir dir(g_strExportDataBaseDir);
    dir.setFilter(QDir::Files);
    QStringList filter;
    QFileInfoList fileList = dir.entryInfoList(filter);
    for (int i = 0; i <fileList.size(); i++)
    {
        QFile file(fileList.at(i).absoluteFilePath());
        if ( !file.remove() )
        {
            PDS_SYS_WARNING_LOG("Fail to remove: %s", fileList.at(i).absoluteFilePath().toLatin1().data());
        }
    };

    switch (eChannelType)
    {
    case CHANNEL_AE://AE参数
    {
        pdb = (SqliteDB*)m_mapDBs[DB_AE];
    }
        break;
    case CHANNEL_TEV://TEV参数
    {
        pdb = (SqliteDB*)m_mapDBs[DB_TEV];
    }
        break;
    case CHANNEL_UHF:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_UHFPRPS];
    }
        break;
    case CHANNEL_HFCT:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_HFCTPRPS];
    }
        break;
    case CHANNEL_MECH:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_MECH];
    }
        break;
    case CHANNEL_TEMPERATURE:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_TEMP];
    }
        break;
    case CHANNEL_HUMIDITY:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_HUMI];
    }
        break;

    //密度微水数据库：
    case CHANNEL_FROST_RAW:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_FROST_RAW];
    }
        break;

    case CHANNEL_FROST_ATM:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_FROST_ATM];
    }
        break;

    case CHANNEL_DEW_RAW:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_DEW_RAW];
    }
        break;

    case CHANNEL_DEW_ATM:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_DEW_ATM];
    }
        break;

    case CHANNEL_MOISTURE:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_MOISTURE];
    }
        break;

    case CHANNEL_PRESS_ABSO:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_PRESS_ABSO];
    }
        break;

    case CHANNEL_PRESS_NORM:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_PRESS_NORM];
    }
        break;

    case CHANNEL_DENSITY:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_DENSITY];
    }
        break;

    case CHANNEL_OXYGEN:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_OXYGEN];
    }
        break;

    case CHANNEL_SF6:
    {
        pdb = (SqliteDB*)m_mapDBs[DB_SF6];
    }
        break;

    default:
        return false;
        break;
    }
    return pdb->exportData(strPointName, listAutoIDBegin, ListAutoIDEnd);
}

/*************************************************
功能： 根据前端ID获取通道名
输入参数：
        ucAddr -- 前端ID
        ucID -- 通道ID
返回：通道名
*************************************************************/
QString DBServer::getChannelName(quint8 ucAddr, quint8 ucID)
{
    ADUChannelType channelType = CHANNEL_INVALID;
    m_configService->getChannelTypeFromID(ucAddr,ucID,channelType);
    QString strChannelName = m_configService->getChanTypName(channelType) + "_" + QString::number(ucAddr) + QString("_channel_") + QString::number(ucID);

    return strChannelName;
}

/*************************************************
功能： 根据前端mac获取通道名
输入参数：
        strmac -- 前端mac
        ucID -- 通道ID
返回：通道名
*************************************************************/
QString DBServer::getChannelName(const QString &strmac, quint8 ucID)
{
    ADUChannelType channelType;
    bool result = m_configService->getChannelTypeFromID(strmac,ucID,channelType);
    QString strChannelName = "";

    if (result)
    {
        strChannelName = m_configService->getChanTypName(channelType) + "_" + strmac + QString("_channel_") + QString::number(ucID);
    }
    return strChannelName;
}

/*************************************************
功能： 获取某通道的最新几条数据中的某字段（ae有效值，tev最大值）
输入参数：
        ucNum -- 获取数据的条数
        strChannelName -- 通道名
返回值：
        所要字段的链表
*************************************************************/
QList<float> DBServer::getAELastRecords(const QString &strChannelName, quint8 ucNum)
{
    QVector<common::DataWrapper> datas;
    Status status = Status::OK();
    if (!(status = m_storageengine->getLastRecord(strChannelName, ucNum, &datas)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    QList<float> rtn;
    std::transform(datas.cbegin(), datas.cend(), std::back_inserter(rtn),
                   [](const common::DataWrapper& itrValue)
    {
        auto unwrapData = itrValue.getData<common::PdAeData>();
        return unwrapData ? unwrapData->aePeakValue : std::numeric_limits<float>::infinity();
    });
    return rtn;
}
QList<float> DBServer::getTEVLastRecords(const QString &strChannelName, quint8 ucNum)
{
    QVector<common::DataWrapper> datas;
    Status status = Status::OK();
    if (!(status = m_storageengine->getLastRecord(strChannelName, ucNum, &datas)).ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
    }
    QList<float> rtn;
    std::transform(datas.cbegin(), datas.cend(), std::back_inserter(rtn),
                   [](const common::DataWrapper& itrValue)
    {
        auto unwrapData = itrValue.getData<common::PdTevData>();
        return unwrapData ? static_cast<float>(unwrapData->level) : std::numeric_limits<float>::infinity();
    });
    return rtn;
}


/*************************************************
函数名： makeRemainStroageEnough()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 确保磁盘有足够剩余空间
*************************************************************/
void DBServer::makeRemainStroageEnough(void)
{
    QStringList query;
    query << "core-z058*";
#ifdef Q_OS_WIN
   /* quint64 tmpI = 100;
    while (tmpI != 0)
    {
        --tmpI;
        delFilesOfQuery(g_strCoreDumpDir, query);
        if (!delDateRecord(getLastRecordDate(), true))
        {
            break;
        }
    }*/
#else
    float curAvailDisk = SystemInfo::getRemainStoragePercent();  //当前磁盘可用率
    if (curAvailDisk < 5)
    {
        if (m_arg)
        {
            m_arg->errorsender.storageSpaceError(curAvailDisk);
        }
    }

    if (curAvailDisk < REMAIN_STORAGE_MIN)
    {
        delFilesOfQuery(g_strCoreDumpDir, query);
        //数据文件清理
        logInfo("#@current disk avail") << curAvailDisk;
        DataFileClean::instance().initFileList();
        DataFileClean::instance().delOldFile();
    }

    float LastAvailDisk = curAvailDisk;     //上次磁盘可用率
    const float EPSINON = 0.000001;         //磁盘变化率精度
    while(curAvailDisk < REMAIN_STORAGE_MIN)
    {        
        //数据库清理
        delOldDateRecord(true);
        //图谱文件清理
        DataFileClean::instance().delOldFile();

        curAvailDisk = SystemInfo::getRemainStoragePercent();
        logInfo(QString("#@cur disk avail:%1,last disk avail:%2").arg(curAvailDisk).arg(LastAvailDisk));
        if(qAbs(curAvailDisk - LastAvailDisk) <= EPSINON) //清理无效
        {
            logInfo("#@clear data Invalid,exit clear step");
            break;
        }
        else
        {
            LastAvailDisk = curAvailDisk;
        }
    }
    DataFileClean::instance().clearFileList();
#endif
}

/*************************************************
函数名： getLastRecordDate()
输入参数： NULL
输出参数： NULL
返回值： 最老的数据的日期
功能： 获取最老的数据的日期
*************************************************************/
QDate DBServer::getLastRecordDate(void)
{
    SqliteDB* pdb = nullptr;
    QDate date = QDate::currentDate();
    for (int i = DB_MECH; i < DB_COUNT; i++)
    {
        if(i == DB_ALARM || i == DB_TASK_RECORD)
        {
            continue;
        }

        pdb = m_mapDBs[i];
        if(pdb)
        {
          date = std::min(date, pdb->getEarliestRecordDate());
        }
    }

    return date;
}

/*************************************************
函数名： delDateRecord
输入参数： date -- 日期
输出参数： NULL
返回值： bool false:数据库目前无数据可删除
功能： 删除指定日期的数据
*************************************************************/
bool DBServer::delDateRecord(const QDate &date, bool limitFlg)
{
    SqliteDB* pdb;
    bool rtnFlg = true;
    for (int i = DB_MECH; i < DB_COUNT; i++)
    {
        if(i == DB_ALARM || i == DB_TASK_RECORD)
        {
            continue;
        }

        pdb = (SqliteDB*)m_mapDBs[i];
        if(pdb)
        {
            if (limitFlg)
            {
                rtnFlg &= pdb->delDateRecordRemainOneDay(date);
            }
            else
            {
                rtnFlg &= pdb->delDateRecord(date);
            }
        }
    }
    Status status = Status::OK();
    if (!(status = m_storageengine->deleteEarliestDayRecord(limitFlg)).ok())
    {
        PDS_SYS_ERR_LOG("%s",status.ToString().c_str());
    }

    //删除数据文件
    QStringList filter;
    QString strData = date.toString("yyyyMMdd");
    filter<<QString("*_%1_*").arg(strData);
    delFilesOfQuery(g_strDataFileDir, filter);
    return rtnFlg;
}

bool DBServer::delOldDateRecord(bool limitFlg)
{
    SqliteDB* pdb = nullptr;
    bool rtnFlg = true;

    for (int i = DB_MECH; i < DB_COUNT; i++)
    {
        if(i == DB_ALARM || i == DB_TASK_RECORD)
        {
            continue;
        }

        pdb = (SqliteDB*)m_mapDBs[i];
        if(pdb)
        {
           rtnFlg &= pdb->delEarliestDataRemainOneDay(limitFlg);
        }
    }
    Status status = Status::OK();
    if (!(status = m_storageengine->deleteEarliestDayRecord(limitFlg)).ok())
    {
        PDS_SYS_ERR_LOG("%s",status.ToString().c_str());
    }
}

/*************************************************
功能： 判断在timeBegin和timeEnd时间范围内，数据库中是否存在前端号为ucAddr、记录号为usRecordID的避雷器记录数据
输入参数：
        ucID：前端ID
        usRecordID：记录ID
        timeBegin：起始日期
        timeEnd：停止日期
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::hasArresterRecord(const QString &strADUID, quint8 ucChannelID, quint16 usRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    VibrationDB* pdb = (VibrationDB*)m_mapDBs[DB_ARRESTER];

    return pdb->hasRecord( getChannelName(strADUID, ucChannelID), usRecordID, timeBegin, timeEnd );
}

/*************************************************
功能： 获取避雷器数据
输入参数：
        strPointId -- 测点id
        recordTime -- 数据时间
        record --避雷器数据
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::getArresterRecord(const QString &strPointId, ChannelPhase eChannelPhase, const QDateTime &recordTime, ArresterRecord &record)
{
    ArresterDB* pdb = (ArresterDB*)m_mapDBs[DB_ARRESTER];

    DataRecord dbRecord;
    bool bRet = pdb->getRecord(strPointId, eChannelPhase, recordTime, dbRecord);

    if ( bRet )
    {
        record = getArresterRecord(dbRecord);
    }
    return bRet;
}
bool DBServer::getArresterRecord(const QString &strPointId, const QDateTime &recordTime, ArresterRecord &record)
{
    ArresterDB* pdb = (ArresterDB*)m_mapDBs[DB_ARRESTER];

    DataRecord dbRecord;
    bool bRet = pdb->getRecord(strPointId, recordTime, dbRecord);

    if ( bRet )
    {
        record = getArresterRecord(dbRecord);
    }
    return bRet;
}

/*************************************************
功能： 判断在timeBegin和timeEnd时间范围内，数据库中是否存在前端号为ucAddr、记录号为usRecordID的避雷器记录数据
输入参数：
        ucID：前端ID
        usRecordID：记录ID
        timeBegin：起始日期
        timeEnd：停止日期
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::hasTEMPRecord(const QString &strADUID, quint8 ucChannelID, quint16 usRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_TEMP];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasHumidityRecord(const QString &strADUID, quint8 ucChannelID, quint16 usRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_HUMI];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

/*************************************************
功能： 判断在timeBegin和timeEnd时间范围内，数据库中是否存在前端号为ucAddr、记录号为usRecordID的避雷器记录数据
输入参数：
        ucID：前端ID
        usRecordID：记录ID
        timeBegin：起始日期
        timeEnd：停止日期
返回值：
        true -- 成功
        else -- 失败
*************************************************************/
bool DBServer::hasVibrationRecord(const QString &strADUID, quint8 ucChannelID, quint16 usRecordID, const QDateTime &timeBegin, const QDateTime &timeEnd )
{
    VibrationDB* pdb = (VibrationDB*)m_mapDBs[DB_VIBRATION];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord( listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasFrostRawRecord( const QString &strADUID,
                        quint8 ucChannelID,
                        quint16 usRecordID,
                        const QDateTime &timeBegin,
                        const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_FROST_RAW];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasFrostAtmRecord( const QString &strADUID,
                        quint8 ucChannelID,
                        quint16 usRecordID,
                        const QDateTime &timeBegin,
                        const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_FROST_ATM];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasDewRawRecord( const QString &strADUID,
                    quint8 ucChannelID,
                    quint16 usRecordID,
                    const QDateTime &timeBegin,
                    const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_DEW_RAW];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasDewAtmRecord( const QString &strADUID,
                      quint8 ucChannelID,
                      quint16 usRecordID,
                      const QDateTime &timeBegin,
                      const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_DEW_ATM];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasMoistureRecord( const QString &strADUID,
                        quint8 ucChannelID,
                        quint16 usRecordID,
                        const QDateTime &timeBegin,
                        const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_MOISTURE];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasPressAbsoRecord( const QString &strADUID,
                         quint8 ucChannelID,
                         quint16 usRecordID,
                         const QDateTime &timeBegin,
                         const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_PRESS_ABSO];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasPressNormRecord( const QString &strADUID,
                         quint8 ucChannelID,
                         quint16 usRecordID,
                         const QDateTime &timeBegin,
                         const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_PRESS_NORM];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasDensityRecord( const QString &strADUID,
                       quint8 ucChannelID,
                       quint16 usRecordID,
                       const QDateTime &timeBegin,
                       const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_DENSITY];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasOxygenRecord( const QString &strADUID,
                      quint8 ucChannelID,
                      quint16 usRecordID,
                      const QDateTime &timeBegin,
                      const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_OXYGEN];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::DBServer::hasSF6Record( const QString &strADUID,
                   quint8 ucChannelID,
                   quint16 usRecordID,
                   const QDateTime &timeBegin,
                   const QDateTime &timeEnd )
{
    ENVDB* pdb = (ENVDB*)m_mapDBs[DB_SF6];

    bool bRet = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, strADUID, ucChannelID))
    {
        bRet = pdb->hasRecord(  listPointArchiveInfo.first().strPointGUID, usRecordID, timeBegin, timeEnd );
    };

    return bRet;
}

bool DBServer::hasSf6StateRecord(const QString &aduId,
                                 quint8 channelId,
                                 quint16 recordId,
                                 const QDateTime &timebegin,
                                 const QDateTime &timeend) const
{
    ENVDB* pdb = reinterpret_cast<ENVDB*>(m_mapDBs[DB_SF6_STATE]);

    bool rtn = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, aduId, channelId))
    {
        rtn = pdb->hasRecord(listPointArchiveInfo.first().strPointGUID, recordId, timebegin, timeend);
    };

    return rtn;
}

bool DBServer::hasWaterLoggingRecord(const QString &aduId,
                                     quint8 channelId,
                                     quint16 recordId,
                                     const QDateTime &timebegin,
                                     const QDateTime &timeend) const
{
    ENVDB* pdb = reinterpret_cast<ENVDB*>(m_mapDBs[DB_ENV_STATUS]);

    bool rtn = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, aduId, channelId))
    {
        rtn = pdb->hasRecord(listPointArchiveInfo.first().strPointGUID, recordId, timebegin, timeend);
    };

    return rtn;
}

bool DBServer::hasSmokeRecord(const QString &aduId, quint8 channelId, quint16 recordId, const QDateTime &timebegin, const QDateTime &timeend) const
{
    ENVDB* pdb = reinterpret_cast<ENVDB*>(m_mapDBs[DB_ENV_STATUS]);

    bool rtn = false;
    QList<PointArchiveInfo> listPointArchiveInfo;

    if (m_configService->getPointArchivesFromChannelID(listPointArchiveInfo, aduId, channelId))
    {
        rtn = pdb->hasRecord(listPointArchiveInfo.first().strPointGUID, recordId, timebegin, timeend);
    };

    return rtn;
}

bool DBServer::getPointIndexRange(const QString &pointId, int64_t *startId, int64_t *endId) const
{
    ::PointConnectionInfo pointInfo;
    auto status = StorageUtils::getPointInfoByPointId(pointId, &pointInfo);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return false;
    }

    auto dbtype = getDatabaseType(pointInfo.etype);
    if (getPointIndexRangeInEngine(dbtype, pointId, startId, endId))
    {
        return true;
    }

    if (m_mapDBs.find(dbtype) == m_mapDBs.cend())
    {
        PDS_SYS_ERR_LOG("can't find such database");
        return false;
    }

    auto db = m_mapDBs[dbtype];
    int tmpAutoId = -1;
    db->getAutoIdInfo(pointId, tmpAutoId);
    if (tmpAutoId <= 0)
    {
        PDS_SYS_ERR_LOG("get autoId failed");
        return false;
    }
    *startId = 1;
    *endId = tmpAutoId;
    return true;
}

/*************************************************
功能： 根据query删除指定目录的符合条件文件
输入参数：
        path -- 文件所在目录
        query -- 删除文件符合的条件
输出参数：
        void
返回值：
        void
*************************************************************/
void DBServer::delFilesOfQuery(const QString& path, const QStringList& query)
{
    QDir dir(path);
    dir.setFilter(QDir::Dirs | QDir::Files);
    dir.setSorting(QDir::DirsFirst);
    QFileInfoList infoList = dir.entryInfoList(query);
    Q_FOREACH(const QFileInfo &info, infoList)
    {
        if (!dir.remove(info.absoluteFilePath()))
        {
            PDS_SYS_WARNING_LOG("delete file: \"%s\" failed, please check it manually", info.absoluteFilePath().toLatin1().data());
        }
    }
}

bool DBServer::getSqliteRecordGlobalIdAndAutoIdByDateTime(DataBaseServiceType dbType, const QString &pointId, const QDateTime &moment, quint64 *globalId, quint64 *autoId)
{
    //makeRemainStroageEnough();//添加数据前先验证数据库剩余磁盘是否充足
    SqliteDB* db = (SqliteDB*)m_mapDBs[dbType];

    if (!db->getRecordByPointIdAndDateTime(pointId, moment, globalId, autoId))
    {
        PDS_SYS_WARNING_LOG("getRecordByPointIdAndDateTime failed");
        return false;
    }
    return true;
}


/************************************
 * 辅控接口
 ************************************/
/*************************************************
函数名： creatDB()
输入参数： stDBDataRecord -- 数据记录结构体
输出参数： NULL
返回值： NULL
功能： 创建数据库
*************************************************************/
void DBServer::createDB( const DBDataRecord &stDBDataRecord )
{
    int iDBIndex;
    createDB(stDBDataRecord, iDBIndex);
}

/*************************************************
函数名： creatDB()
输入参数： stDBDataRecord -- 数据记录结构体
输出参数： iDBIndex -- 数据库索引
返回值： NULL
功能： 创建数据库
*************************************************************/
void DBServer::createDB( const DBDataRecord &stDBDataRecord, int &iDBIndex)
{
    DataBase stDataBase;
    storage::DataBaseConfig stDataBaseConfig = *storage::g_stDataBases[DB_FUKONG].pDataBaseConfig;
//    auto tableconfig = DBConfig::g_stDataBases[DB_FUKONG].pDataBaseConfig->pstTableConfigs;

    stDataBaseConfig.strDataBaseName = stDBDataRecord.strPointType;
//    QList<TableItemConfig*> pstTableItemConfigs;
//    for ( int i = 0; i< DB_DATA_RECORD_COUNT ; i++ )
//    {
//        pstTableItemConfigs.append(&s_TableConfigs[i]);
//    }
//    stDataBaseConfig.pstTableConfigs->pstTableItemConfigs = pstTableItemConfigs;

//    stDataBaseConfig.pstTableConfigs->usTableItemCount = DB_DATA_RECORD_COUNT;

    iDBIndex = m_listFuKongDBName.size();
    m_listFuKongDBName.append( stDBDataRecord.strPointType );

    stDataBase.pDataBaseConfig = &stDataBaseConfig;

    m_mapDBs[iDBIndex] = new SqliteDB( stDataBase );
    m_mapDBs[iDBIndex]->moveToThread(m_pThread);
    m_mapDBs[iDBIndex]->open();     //TODO: 需要关闭

    m_mapDataBaseConfig.insert(stDBDataRecord.strPointType, stDataBase);
}

void DBServer::addOrUpdateRecord(const DBDataRecord &stDBDataRecord)
{
    if ( isRecordExist(stDBDataRecord) )
    {
        updateFuKongRecord(stDBDataRecord);
    }
    else
    {
        addRecord(stDBDataRecord);
    }
}

/*************************************************
函数名： getDBIndex
输入参数： stDBDataRecord -- 数据记录结构体
输出参数： NULL
返回值： 数据库索引
功能： 获取库的索引（当多个线程同时访问一个未创立的库时，会因库的创建耗时导致程序崩溃，所以添加该逻辑）
*************************************************************/
int DBServer::getDBIndex( const DBDataRecord &stDBDataRecord )
{
    QMutexLocker qMutexLocker(&m_dbMutex);

    int iDBIndex = m_listFuKongDBName.indexOf( stDBDataRecord.strPointType );

    if ( -1 == iDBIndex )
    {
        //创建库
        createDB( stDBDataRecord, iDBIndex );
    }

    return iDBIndex;
}

/*************************************************
函数名： addRecord()
输入参数： stDBDataRecord -- 数据记录结构体
输出参数： missingId -- 缺失数据索引
返回值： NULL
功能： 新增数据
*************************************************************/
void DBServer::addRecord(const DBDataRecord &stDBDataRecord)
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, stDBDataRecord.timeRecord.toString(DATETIME_FORMAT) );
    dbRecord.setData( IK_RECORDTIME_T, stDBDataRecord.timeRecord.toTime_t() );
    dbRecord.setData( IK_POINT_ID, stDBDataRecord.strPointID );
    dbRecord.setData( IK_DATA_FILE_PATH, stDBDataRecord.strDataFilePath );
    dbRecord.setData( IK_DATA_RECORD_ID, stDBDataRecord.strRecordID );

    if (stDBDataRecord.dataIndex >= 0)
    {
        dbRecord.setData(IK_DATA_INDEX_ID, stDBDataRecord.dataIndex);
    }

    QJsonObject jsonData;
    for (int i = 0; i < stDBDataRecord.listDataInfo.size(); i++)
    {
        QString strDataName = stDBDataRecord.listDataInfo.keys().at(i);
        jsonData.insert( strDataName, stDBDataRecord.listDataInfo.value(strDataName).strDataValue );
    }
    dbRecord.setData( DB_DATA_RECORD_DATA, QJsonDocument(jsonData).toJson() );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];
    pdb->addRecord(dbRecord);
}

/*************************************************
函数名： updateRecord()
输入参数： stDBDataRecord -- 数据记录结构体
输出参数： NULL
返回值： NULL
功能： 更新数据
*************************************************************/
void DBServer::updateFuKongRecord(const DBDataRecord &stDBDataRecord)
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    DataRecord dbRecord;
    dbRecord.setData( IK_RECORDTIME, stDBDataRecord.timeRecord.toString( DATETIME_FORMAT ) );
    dbRecord.setData( IK_RECORDTIME_T, stDBDataRecord.timeRecord.toTime_t() );
    dbRecord.setData( IK_POINT_ID, stDBDataRecord.strPointID );
    dbRecord.setData( IK_DATA_FILE_PATH, stDBDataRecord.strDataFilePath );
    dbRecord.setData( IK_DATA_RECORD_ID, stDBDataRecord.strRecordID );

    QJsonObject jsonData;
    for (int i = 0; i < stDBDataRecord.listDataInfo.size(); i++)
    {
        QString strDataName = stDBDataRecord.listDataInfo.keys().at(i);
        jsonData.insert( strDataName, stDBDataRecord.listDataInfo.value( strDataName ).strDataValue );
    }
    dbRecord.setData( DB_DATA_RECORD_DATA, QJsonDocument( jsonData ).toJson() );

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    pdb->updateFuKongRecord( dbRecord );
}

/*************************************************
函数名： getRecord()
输入参数： stDBDataRecord -- 数据记录结构体（测点类型，测点名，各数据字段的名称）
输出参数： stDBDataRecord -- 数据记录结构体
返回值： NULL
功能： 获取数据
*************************************************************/
bool DBServer::getRecordByAutoID( DBDataRecord &stDBDataRecord )
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    DataRecord dbRecord;
    if ( !pdb->getRecord( stDBDataRecord.strPointID, stDBDataRecord.iDataID, dbRecord ) )
    {
        return false;
    }

    return getRecord( dbRecord, stDBDataRecord);
}

bool DBServer::isRecordExist(const DBDataRecord &stDBDataRecord)
{
    bool bRes = false;
    int nIndex = getDBIndex(stDBDataRecord);
    if ( m_mapDBs.contains(nIndex) )
    {
        SqliteDB* pdb = (SqliteDB*)m_mapDBs[nIndex];
        DataRecord dbRecord;
        bRes = pdb->getRecord(stDBDataRecord.strPointID, stDBDataRecord.strRecordID, dbRecord);
    }

    return bRes;
}

/*************************************************
函数名： getRecord()
输入参数： stDBDataRecord -- 数据记录结构体（测点类型，测点名，各数据字段的名称）
输出参数： stDBDataRecord -- 数据记录结构体
返回值： NULL
功能： 获取数据
*************************************************************/
bool DBServer::getRecordByRecordID(DBDataRecord &stDBDataRecord )
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    DataRecord dbRecord;
    if ( !pdb->getRecord( stDBDataRecord.strPointID, stDBDataRecord.strRecordID, dbRecord ) )
    {
        return false;
    }

    return getRecord( dbRecord, stDBDataRecord);
}

/*************************************************
函数名： getRecord()
输入参数： stDBDataRecord -- 数据记录结构体（测点类型，测点名，各数据字段的名称，数据时间）
输出参数： stDBDataRecord -- 数据记录结构体
返回值： NULL
功能： 获取数据
*************************************************************/
bool DBServer::getRecordByDateTime( DBDataRecord &stDBDataRecord )
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    DataRecord dbRecord;
    if ( !pdb->getRecord( stDBDataRecord.strPointID, stDBDataRecord.timeRecord, dbRecord ) )
    {
        return false;
    }

    return getRecord( dbRecord, stDBDataRecord);
}

/*************************************************
函数名： getRecord()
输入参数： stDBDataRecord -- 数据记录结构体（测点类型，测点名，各数据字段的名称）
        strRecordName -- 查询的字段名
        strRecordData -- 查询的字段值
输出参数： stDBDataRecord -- 数据记录结构体
返回值： NULL
功能： 获取数据
*************************************************************/
bool DBServer::getRecord( DBDataRecord &stDBDataRecord, const QString &strRecordName, const QString &strRecordData )
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    DataRecord dbRecord;
    if ( !pdb->getRecord( stDBDataRecord.strPointID, strRecordName, strRecordData, dbRecord ) )
    {
        m_arg->errorsender.datarecordError(QString("get data failed, pointId: %1").arg(stDBDataRecord.strPointID));
        return false;
    }

    return getRecord( dbRecord, stDBDataRecord);
}

/*************************************************
函数名： getRecord()
输入参数： stDBDataRecord -- 数据记录结构体(测点类型、测点名、各数据字段的名称)
        iSatarIndex -- 起始数据条数（从第几条开始）
        iSize -- 查询结果条数（输出几条）
输出参数： listDBDataRecords -- 数据记录结构体列表
返回值： NULL
功能： 获取数据
*************************************************************/
bool DBServer::getRecords(const DBDataRecord &stDBDataRecord, int iSatarIndex, int iSize, QList<DBDataRecord> &listDBDataRecords, bool bASC )
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    QList<DataRecord> dbRecords;
    if ( !pdb->getRecords( stDBDataRecord.strPointID, iSatarIndex, iSize, dbRecords, bASC ) )
    {
        if (m_arg)
        {
            m_arg->errorsender.datarecordError(QString("get data failed, pointId: %1").arg(stDBDataRecord.strPointID));
        }
        return false;
    }
    for( int i = 0; i < dbRecords.size(); i++)
    {
        DBDataRecord stDataRecord = stDBDataRecord;

        getRecord( dbRecords.at(i), stDataRecord);

        listDBDataRecords.append(stDataRecord);
    }

    return true;
}

/*************************************************
函数名： getRecord()
输入参数： stDBDataRecord -- 数据记录结构体(测点类型、测点名)
输出参数： iRecordCount -- 数据记录数量
返回值： NULL
功能： 获取数据记录数量
*************************************************************/
bool DBServer::getRecordCount(const DBDataRecord &stDBDataRecord, int64_t &iRecordCount )
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    return pdb->getTotalTableDataNum( stDBDataRecord.strPointID, iRecordCount );
}

/*************************************************
函数名： getLastRecordDateTime()
输入参数： stDBDataRecord -- 数据记录结构体(测点类型、测点名)
输出参数： recordTime -- 数据记录时间
返回值： NULL
功能： 获取最新数据记录时间
*************************************************************/
bool DBServer::getLastRecordDateTime(const DBDataRecord &stDBDataRecord, QDateTime &recordTime )
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    return pdb->getLastRecordDateTime( stDBDataRecord.strPointID, recordTime );
}


/*************************************************
函数名： getLastRecordDateTime()
输入参数： stDBDataRecord -- 数据记录结构体(测点类型、测点名)
输出参数： recordTime -- 数据记录时间
返回值： NULL
功能： 获取最新数据记录时间
*************************************************************/
bool DBServer::getLastRecord(DBDataRecord &stDBDataRecord)
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    DataRecord dbRecord;
    if ( !pdb->getLastRecord( stDBDataRecord.strPointID, dbRecord ) )
    {
        if (m_arg)
        {
            m_arg->errorsender.datarecordError(QString("get data failed, pointId: %1").arg(stDBDataRecord.strPointID));
        }
        return false;
    }

    return getRecord( dbRecord, stDBDataRecord);
}

/*************************************************
函数名： getRecordFilePath()
输入参数： stDBDataRecord -- 数据记录结构体(测点类型、测点id,数据时间)
输出参数： strFilePath -- 数据文件路径
返回值： 操作结果
功能： 获取数据记录文件路径
*************************************************************/
bool DBServer::getRecordFilePath( const DBDataRecord &stDBDataRecord, QString &strFilePath)
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];
    bool bRes = pdb->getRecordFilePath( stDBDataRecord.strPointID, stDBDataRecord.timeRecord, strFilePath );
    if ( strFilePath.isEmpty() )
    {
        bRes = false;
    }

    return bRes;
}

/*************************************************
函数名： getRecord()
输入参数： stDBDataRecord -- 数据记录结构体(测点类型、测点名、各数据字段的名称)
        startTime -- 起始时间
        endTime -- 结束时间
输出参数： listDBDataRecords -- 数据记录结构体列表
返回值： NULL
功能： 获取数据
*************************************************************/
bool DBServer::getRecords( const DBDataRecord &stDBDataRecord, const QDateTime &startTime, const QDateTime &endTime, QList<DBDataRecord> &listDBDataRecords )
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    QList<DataRecord> dbRecords;
    if ( !pdb->getRecords( stDBDataRecord.strPointID, startTime, endTime, dbRecords ) )
    {
        if (m_arg)
        {
            m_arg->errorsender.datarecordError(QString("get data failed, pointId: %1").arg(stDBDataRecord.strPointID));
        }
        return false;
    }
    for( int i = 0; i < dbRecords.size(); i++)
    {
        DBDataRecord stDataRecord = stDBDataRecord;

        getRecord( dbRecords.at(i), stDataRecord);

        listDBDataRecords.append(stDataRecord);
    }

    return true;
}

/*************************************************
输入参数： stDBDataRecord -- 数据记录结构体(测点类型、测点名、各数据字段的名称)
        startTime -- 起始时间
        endTime -- 结束时间
输出参数： listDBDataRecords -- 趋势数据列表
返回值： NULL
功能： 获取数据
*************************************************************/
bool DBServer::getTrendRecords(const DBDataRecord &stDBDataRecord, const QDateTime &startTime, const QDateTime &endTime, QList<DBDataRecord> &listDBDataRecords, int iLimit )
{
    int iDBIndex = getDBIndex(stDBDataRecord);

    SqliteDB* pdb = (SqliteDB*)m_mapDBs[iDBIndex];

    QList<DataRecord> dbRecords;
    if ( !pdb->getTrendRecords( stDBDataRecord.strPointID, startTime, endTime, dbRecords, iLimit ) )
    {
        if (m_arg)
        {
            m_arg->errorsender.datarecordError(QString("get data failed, pointId: %1").arg(stDBDataRecord.strPointID));
        }
        return false;
    }
    for( int i = 0; i < dbRecords.size(); i++)
    {
        DBDataRecord stDataRecord = stDBDataRecord;
        getRecord( dbRecords.at(i), stDataRecord);

        listDBDataRecords.append(stDataRecord);
    }

    return true;
}

bool DBServer::onUpdateStationId(const QString& newStationId)
{
    bool rtn = true;
    auto keys = m_mapDBs.keys();
    for (auto i : keys)
    {
        if (!reinterpret_cast<SqliteDB*>(m_mapDBs[i])->updateStationId(newStationId))
        {
            rtn = false;
        }
    }
    return rtn;
}

/*************************************************
输入参数： dbRecord -- 数据列表
输出参数： stDBDataRecord -- 数据结构体
返回值： 操作结果
功能： 解析数据
*************************************************************/
bool DBServer::getRecord( const DataRecord &dbRecord, DBDataRecord &stDBDataRecord )
{
    stDBDataRecord.timeRecord = dbRecord.value( IK_RECORDTIME ).toDateTime();
    stDBDataRecord.iDataID = dbRecord.value( IK_ID ).toInt();
    stDBDataRecord.strDataFilePath = dbRecord.value( IK_DATA_FILE_PATH ).toString();
    stDBDataRecord.strRecordID = dbRecord.value( IK_DATA_RECORD_ID ).toString();

    for ( int i = 0; i < stDBDataRecord.listDataInfo.count(); i++ )
    {
        QByteArray arrayData = dbRecord.value( DB_DATA_RECORD_DATA ).toByteArray();
        QJsonObject jsonData = QJsonDocument::fromJson( arrayData ).object();
        QString strDataName = stDBDataRecord.listDataInfo.keys().at(i);
        DBDataInfo stDBDataInfo = stDBDataRecord.listDataInfo.value(strDataName);
        stDBDataInfo.strDataValue = jsonData.value( strDataName ).toString();
        stDBDataRecord.listDataInfo.insert( strDataName, stDBDataInfo );
    }

    bool bRet = false;
    if ( stDBDataRecord.timeRecord.isValid() )
    {
        bRet = true;
    }
    return bRet;
}

DataRecordList DBServer::getDataByIndex(DataBaseServiceType dbType,
                                        const QString& pointId,
                                        qint64 dataindexstart,
                                        qint64 dataindexend) const
{
    DataRecordList rtn;
    SqliteDB* db = static_cast<SqliteDB*>(m_mapDBs[dbType]);

    if (!db->getRecordByIndex(pointId, IK_ID, dataindexstart, std::max(dataindexstart, dataindexend), &rtn))
    {
        PDS_SYS_ERR_LOG("getRecordByIndex failed!");
        return {};
    }
    return rtn;
}

DataRecordList DBServer::getDataBeforeMoment(DataBaseServiceType dbType,
                                             const QString &pointId,
                                             const QDateTime &moment,
                                             qint64 recordsize) const
{
    DataRecordList rtn;
    SqliteDB* db = static_cast<SqliteDB*>(m_mapDBs[dbType]);

    if (!db->getRecordBeforeMomentByKeyColumn(pointId, IK_RECORDTIME_T, moment, recordsize, &rtn))
    {
        PDS_SYS_ERR_LOG("getRecordBeforeMomentByKeyColumn failed!");
        return {};
    }
    return rtn;
}

DataRecordList DBServer::getDataByScanTime(DataBaseServiceType dbType, const QString &pointId, const QDateTime &start, const QDateTime &end) const
{
    DataRecordList rtn;
    SqliteDB* db = static_cast<SqliteDB*>(m_mapDBs[dbType]);

    if (!db->getRecordByTimeScan(pointId, IK_RECORDTIME_T, start, end, &rtn))
    {
        PDS_SYS_ERR_LOG("getRecordByTimeScan failed!");
        return {};
    }
    return rtn;
}

bool DBServer::getAutoIdInfo(DataBaseServiceType dbtype, const QString &strPointID, int &iMaxAutoId, int &iMinAutoId, int &maxRecordTimeT, int &minRecordTimeT) const
{
    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        QDateTime earliestTime;
        QDateTime latestTime;
        uint64_t maxIndex = 0;
        uint64_t minIndex = 0;
        auto status = m_storageengine->getAutoIndexRange(strPointID, &earliestTime, &latestTime, &minIndex, &maxIndex);
        if (!status.ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
        iMaxAutoId = static_cast<int>(maxIndex);
        iMinAutoId = static_cast<int>(minIndex);
        maxRecordTimeT = latestTime.toTime_t();
        minRecordTimeT = earliestTime.toTime_t();
        break;
     }
    default:
        return false;
    }
    return true;
}

bool DBServer::getRecordIndex(DataBaseServiceType dbtype, quint64 &startIndex, quint64 &endIndex) const
{
    common::DataWrapper tmpdata;
    switch (dbtype)
    {
    case DB_AE:
        tmpdata.setData(common::PdAeData());
        break;
    case DB_TEV:
        tmpdata.setData(common::PdTevData());
        break;
    case DB_UHFPRPS:
        tmpdata.setData(common::PdUhfData());
        break;
    case DB_HFCTPRPS:
        tmpdata.setData(common::PdHfctData());
        break;
    case DB_TEVPRPS:
        tmpdata.setData(common::PdTEVPRPSData());
        break;
    default:
        return false;
    }
    auto status = m_storageengine->getGlobalIndexRange(tmpdata, &startIndex, &endIndex);
    if (!status.ok())
    {
        PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
        return false;
    }
    return true;
}

bool DBServer::lastRecordId(DataBaseServiceType dbtype, const QString &strPointId, int* recordId) const
{
    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        common::DataWrapper data;
        auto status = m_storageengine->getLastRecord(strPointId, &data);
        if (!status.ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
        *recordId = static_cast<int>(data.getData()->dataIndex);
        break;
    }
    default:
        return false;
    }
    return true;
}

bool DBServer::nearbyRecordId(DataBaseServiceType dbtype,
                              const QString &pointId,
                              int recordId,
                              int *predId,
                              int *nextId) const
{
    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        uint64_t prevIndex = 0;
        uint64_t nextIndex = 0;
        auto status = m_storageengine->getNearbyRecordIndex(pointId, recordId, &prevIndex, &nextIndex);
        if (!status.ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
        *predId = static_cast<int>(prevIndex);
        *nextId = static_cast<int>(nextIndex);
        break;
    }
    default:
        return false;
    }
    return true;
}

bool DBServer::getTrendData(DataBaseServiceType dbtype,
                            const QString &pointId,
                            const QDateTime &startDate,
                            const QDateTime &endDate,
                            uint64_t limit,
                            QList<QDateTime> *dateTimes,
                            QList<float> *datas) const
{
    auto aeTrans = [](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdAeData>();
        if (unwrapData)
        {
            return std::make_pair(unwrapData->getRecordTime(), unwrapData->aePeakValue);
        }
        return std::make_pair(QDateTime{}, std::numeric_limits<float>::infinity());
    };

    auto tevTrans = [](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdTevData>();
        if (unwrapData)
        {
            return std::make_pair(unwrapData->getRecordTime(), static_cast<float>(unwrapData->level));
        }
        return std::make_pair(QDateTime{}, std::numeric_limits<float>::infinity());
    };

    auto defaultTransFunc = [](const common::DataWrapper&)
    {return std::make_pair(QDateTime{}, std::numeric_limits<float>::infinity());};

    std::map<DataBaseServiceType, std::function<std::pair<QDateTime, float>(const common::DataWrapper&)>> transformFuncs =
    {
        {DB_AE, aeTrans},
        {DB_TEV, tevTrans},
        {DB_UHFPRPS, defaultTransFunc},
        {DB_HFCTPRPS, defaultTransFunc},
        {DB_TEVPRPS, defaultTransFunc},
    };

    QVector<common::DataWrapper> tmpDataSet;

    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        auto status = m_storageengine->getSampleDataOnTimePeriod(pointId, limit, startDate, endDate, &tmpDataSet);
        if (!status.ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
        std::for_each(tmpDataSet.cbegin(), tmpDataSet.cend(), [&](const common::DataWrapper& itrValue)
        {
            auto itr = transformFuncs.find(dbtype);
            if (itr != transformFuncs.cend())
            {
                auto tmpPairData = itr->second(itrValue);
                dateTimes->push_back(tmpPairData.first);
                datas->push_back(tmpPairData.second);
            }
        });
        break;
    }
    default:
        return false;
    }
    return true;
}

bool DBServer::getTrendDataRecords(ADUChannelType channelType,
                                   const QString &pointId,
                                   const QDateTime &startDate,
                                   const QDateTime &endDate,
                                   uint64_t limit,
                                   QVariantList* datalist) const
{
    PDS_SYS_INFO_LOG("getTrendDataRecords start");
    ExitGuard exitguard([]{ PDS_SYS_INFO_LOG("getTrendDataRecords end");});
    if (nullptr == datalist)
    {
        PDS_SYS_ERR_LOG("datalist param is nullptr");
        return false;
    }

    DataBaseServiceType dbtype = getDatabaseType(channelType);

    if (getTrendDataRecordsInEngine(dbtype, pointId, startDate, endDate, limit, datalist))
    {
        return true;
    }

    auto itr = m_mapDBs.find(dbtype);
    if (itr == m_mapDBs.cend())
    {
        PDS_SYS_ERR_LOG("dbtype:%d maybe hasn't been implemented", static_cast<int32_t>(dbtype));
        return false;
    }

    SqliteDB* db = itr.value();
    DataRecordList tmpDataList;
    if (!db->getTrendRecords(pointId, startDate, endDate, tmpDataList, limit))
    {
        PDS_SYS_ERR_LOG("getTrendRecords failed");
        return false;
    }

    auto functorItr = m_transformFunctors.find(channelType);
    if (functorItr == m_transformFunctors.cend())
    {
        PDS_SYS_ERR_LOG("channel type:%d hasn't been implemented in this method", static_cast<int32_t>(channelType));
        return false;
    }
    datalist->clear();
    std::transform(tmpDataList.cbegin(), tmpDataList.cend(), std::back_inserter(*datalist), functorItr->second);

    return true;
}

bool DBServer::getTrendDataRecordsInEngine(DataBaseServiceType dbtype,
                                           const QString &pointId,
                                           const QDateTime &startDate,
                                           const QDateTime &endDate,
                                           uint64_t limit,
                                           QVariantList *datalist) const
{
    auto aeTrans = [&pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdAeData>();
        return unwrapData ?
                    QVariant::fromValue(DataTypeConvertionUtils::convertFromPdAeData(pointId, *unwrapData)) :
                    QVariant{};
    };

    auto tevTrans = [&pointId](const common::DataWrapper& input)
    {
        auto unwrapData = input.getData<common::PdTevData>();
        return unwrapData ?
                    QVariant::fromValue(DataTypeConvertionUtils::convertFromPdTevData(pointId, *unwrapData)) :
                    QVariant{};
    };

    auto defaultTransFunc = [](const common::DataWrapper&)
    {return QVariant{};};

    std::map<DataBaseServiceType, std::function<QVariant(const common::DataWrapper&)>> transformFuncs =
    {
        {DB_AE, aeTrans},
        {DB_TEV, tevTrans},
        {DB_UHFPRPS, defaultTransFunc},
        {DB_HFCTPRPS, defaultTransFunc},
        {DB_TEVPRPS, defaultTransFunc},
    };


    QVector<common::DataWrapper> tmpDataSet;

    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        auto status = m_storageengine->getSampleDataOnTimePeriod(pointId, limit, startDate, endDate, &tmpDataSet);
        if (!status.ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
        std::for_each(tmpDataSet.cbegin(), tmpDataSet.cend(), [&](const common::DataWrapper& itrValue)
        {
            auto itr = transformFuncs.find(dbtype);
            if (itr != transformFuncs.cend())
            {
                datalist->push_back(itr->second(itrValue));
            }
            else
            {
                return QVariant{};
            }
        });
        break;
    }
    default:
        return false;
    }
    return true;
}

bool DBServer::getPointIndexRangeInEngine(DataBaseServiceType dbtype, const QString &pointId, int64_t *startId, int64_t *endId) const
{
    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        common::DataWrapper data;
        auto status = m_storageengine->getLastRecord(pointId, &data);
        if (!status.ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
        *startId = 1;
        *endId = data.getData()->dataIndex;
        break;
    }
    default:
        return false;
    }
    return true;
}

bool DBServer::getLastRecordDateTimeInEngine(DataBaseServiceType dbtype, const QString &pointId, QDateTime *datetime) const
{
    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        common::DataWrapper data;
        auto status = m_storageengine->getLastRecord(pointId, &data);
        if (!status.ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
        *datetime = data.getData()->getRecordTime();
        break;
    }
    default:
        return false;
    }
    return true;
}

bool DBServer::getRecordIDListFromDateTimeInEngine(DataBaseServiceType dbtype,
                                           const QString &pointId,
                                           const QDateTime &timeBegin,
                                           const QDateTime &timeEnd,
                                           QList<quint16> *listRecordID) const
{
    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        QVector<common::DataWrapper> tmpdatas;
        auto status = m_storageengine->getRecordInTimePeriod(pointId, timeBegin, timeEnd, &tmpdatas);
        if (!status.ok())
        {
            return false;
        }
        listRecordID->clear();
        std::transform(tmpdatas.cbegin(), tmpdatas.cend(), std::back_inserter(*listRecordID),
                       [](const common::DataWrapper& itrValue){
            //return static_cast<quint16>(itrValue.getData()->dataIndex);
            return static_cast<quint16>(itrValue.getData()->recordId.toInt());
        });
        break;
    }
    default:
        return false;
    }
    return true;
}

bool DBServer::getLastRecordIdInEngine(DataBaseServiceType dbtype, const QString &pointId, int32_t *recordId) const
{
    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        common::DataWrapper data;
        auto status = m_storageengine->getLastRecord(pointId, &data);
        if (!status.ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
        *recordId = static_cast<int32_t>(data.getData()->dataIndex);
        break;
    }
    default:
        return false;
    }
    return true;
}

bool DBServer::getTotalTableDataNumInEngine(DataBaseServiceType dbtype, const QString &pointId, int64_t &count) const
{
    switch (dbtype)
    {
    case DB_AE:
    case DB_TEV:
    case DB_UHFPRPS:
    case DB_HFCTPRPS:
    case DB_TEVPRPS:
    {
        auto status = m_storageengine->getTotalTableDataNum(pointId, count);
        if (!status.ok())
        {
            PDS_SYS_ERR_LOG("%s", status.ToString().c_str());
            return false;
        }
        break;
    }
    default:
        return false;
    }
    return true;
}

void DBServer::onAddAduAlarmRecordSlot(AduAlarmRecord stAlarmRecord)
{
    if(!hasAduAlarmRecord(stAlarmRecord.strAduID, stAlarmRecord.qui64RecordTimeT, stAlarmRecord.qui16DataID))
    {
        SqliteDB* pdb = m_mapDBs[DB_ALARM];

        if(pdb)
        {
            DataRecord dbRecord;
            dbRecord.setData(IK_GLOBAL_ID, pdb->updateGlobalID(pdb->getGlobalID(ALARM_GLOBAL_ID_START) ));
            dbRecord.setData(IK_RECORDTIME, QDateTime::fromMSecsSinceEpoch(stAlarmRecord.qui64RecordTimeT).toString(DATETIME_FORMAT));
            dbRecord.setData(IK_RECORDTIME_T, stAlarmRecord.qui64RecordTimeT);
            dbRecord.setData(ALARM_ADU_NAME, stAlarmRecord.strAduID);
            dbRecord.setData(ADU_ALARM_DATA_ID, stAlarmRecord.qui16DataID);
            dbRecord.setData(ADU_ALARM_TYPE, stAlarmRecord.qui16AlarmType);
            dbRecord.setData(ADU_ALARM_DATA, stAlarmRecord.strData);

            pdb->addRecord(dbRecord, ADU_ALARM_TABLE);
        }
    }
    else
    {
        logWarnning(QString("Adu:%1, Alarm Data:(time:%2, DataID:%3) Already Exists")
                    .arg(stAlarmRecord.strAduID).arg(stAlarmRecord.qui64RecordTimeT).arg(stAlarmRecord.qui16DataID));
    }
}

bool DBServer::convertToDataRecordKey(const DataRecord &record, common::DataRecordKey &key) const
{
    key.iAutoID = record.value(IK_ID).toInt();
    key.recordTime = QDateTime::fromTime_t(record.value(IK_RECORDTIME_T).toLongLong());
    key.iRecordID = record.value(SAMPLE_DATA_RECORD_ID).toInt();
    return true;
}

bool DBServer::convertToSensorStatus(const DataRecord& record, common::SensorStatus& status) const
{
    status.iBatteryVoltage = record.value(SAMPLE_DATE_REMAIN_TIME).toInt();
    status.iSignalStrength = record.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    status.iSnr = record.value(SAMPLE_NOISE_RATIO).toInt();
    status.recordTime = QDateTime::fromTime_t(record.value(IK_RECORDTIME_T).toLongLong());
    return true;
}

bool DBServer::getFieldConfigByDataType(DBServer::TrendDataType dataType, ADUChannelType &channelType, ItemKeyWrapper &fieldKey, bool &isJsonFormat, QString &jsonFieldName)
{
    // 默认值
    isJsonFormat = false;
    jsonFieldName = "";

    switch(dataType)
    {
    // 环境数据（JSON格式）
    case TrendDataType::Temperature:
        channelType = CHANNEL_TEMPERATURE;
        fieldKey = TEMP_DATA;
        isJsonFormat = true;
        jsonFieldName = "value";
        break;

    case TrendDataType::Humidity:
        channelType = CHANNEL_HUMIDITY;
        fieldKey = HUMI_DATA;
        isJsonFormat = true;
        jsonFieldName = "value";
        break;

    // SF6相关数据（直接数值）
    case TrendDataType::SF6_Pressure:
        channelType = CHANNEL_SF6;
        fieldKey = SF6_PRESSURE;
        break;

    case TrendDataType::SF6_Density:
        channelType = CHANNEL_SF6;
        fieldKey = SF6_DENSITY;
        break;

    case TrendDataType::SF6_Temperature:
        channelType = CHANNEL_SF6;
        fieldKey = SF6_TEMPRETURE;
        break;

    case TrendDataType::SF6_EnvTemperature:
        channelType = CHANNEL_SF6;
        fieldKey = SF6_ENV_TEMPERATURE;
        break;

    case TrendDataType::SF6_SuperCapVoltage:
        channelType = CHANNEL_SF6;
        fieldKey = SF6_SUPERCAP_VOLTAGE;
        break;

    default:
        return false;
    }

    return true;
}

bool DBServer::validatePointDataType(const QString &pointId, TrendDataType dataType)
{
    // 获取测点对应的通道类型
    const QList<ADUChannelType> channelTypeList = m_configService->getChannelTypeListFromPoint(pointId);
    if (channelTypeList.isEmpty())
    {
        logError("get channel type list from point failed, pointId: ") << pointId;
        return false;
    }

    // 获取数据类型对应的通道类型
    ADUChannelType expectedChannelType;
    ItemKeyWrapper fieldKey;
    bool isJsonFormat;
    QString jsonFieldName;

    if(!getFieldConfigByDataType(dataType, expectedChannelType, fieldKey, isJsonFormat, jsonFieldName))
    {
        return false;
    }

    // 验证测点的通道类型是否匹配
    ADUChannelType actualChannelType = channelTypeList.first();

    return actualChannelType == expectedChannelType;
}

bool DBServer::getAutoIdInfo(DataBaseServiceType dbtype, const QString &strPointID, int &iMaxAutoId) const
{
    int minIndex = 0;
    int earliestDate = 0;
    int latestDate = 0;
    return getAutoIdInfo(dbtype, strPointID, iMaxAutoId, minIndex, earliestDate, latestDate);
}

RecordBase DBServer::getRecordBase(const DataRecord& record) const
{
    RecordBase rtn;
    rtn.recordTime = record.value(IK_RECORDTIME).toDateTime();
    rtn.autoId = record.value(IK_ID).toInt();
    rtn.recordStringId = record.value(SAMPLE_DATA_DATA_ID).toString();
    rtn.isUpdate = record.value(SAMPLE_DATA_IS_DATA_UPDATE).toInt();
    rtn.pointArchiveInfo.strDeviceGUID = record.value(SAMPLE_DATA_DEVICE_ID).toString();
    rtn.pointArchiveInfo.strDevicePMS = record.value(SAMPLE_DATA_DEVICE_PMS).toString();
    rtn.pointArchiveInfo.strPointGUID = record.value(SAMPLE_DATA_POINT_GUID).toString();
    rtn.pointArchiveInfo.strStationGUID = record.value(SAMPLE_DATA_STATION_ID).toString();
    rtn.pointArchiveInfo.strStationPMS = record.value(SAMPLE_DATA_STATION_PMS).toString();

    TestPointInfo testPointInfo;
    m_configService->getTestPoint(rtn.pointArchiveInfo.strPointGUID, testPointInfo);
    rtn.pointArchiveInfo.strPointName = testPointInfo.strName;

    const int iBattery = record.value(SAMPLE_DATA_BATTERY).toInt();
    const int iRemainTime = record.value(SAMPLE_DATE_REMAIN_TIME).toInt();
    rtn.batteryInfo = iBattery + iRemainTime * 10;
    rtn.signalstrength = record.value(SAMPLE_SIGNAL_STRENGTH).toInt();
    rtn.noiseratio = record.value(SAMPLE_NOISE_RATIO).toInt();
    rtn.recordId = record.value(SAMPLE_DATA_RECORD_ID).toInt();
    rtn.globalId = record.value(IK_GLOBAL_ID).toLongLong();
    rtn.channelId = getChannelInfo(record.value(SAMPLE_DATA_CHANNEL_NAME).toString(), rtn.aduId);
    return rtn;
}

bool DBServer::addUpgradeTaskRecord( const UpgradeTaskRecord &record )
{
    SqliteDB* pdb = m_mapDBs[DB_TASK_RECORD];

    DataRecord dbRecord;
    dbRecord.setData(IK_RECORDTIME_T, record.taskCreateTime.toMSecsSinceEpoch());
    dbRecord.setData(UPGRADE_TASK_ID, record.taskId);
    dbRecord.setData(UPGRADE_TASK_NAME, record.taskName);
    dbRecord.setData(UPGRADE_FIRMWARE_NAME, record.FirmwareName);
    dbRecord.setData(UPGRADE_DEVICETYPE, record.deviceType);

    return pdb->addRecord(dbRecord, UPGRADE_TASKS_TABLE);
}

bool DBServer::addUpgradeTaskDetailsRecord(const QList<UpgradeTaskDetails> &recordList)
{
    SqliteDB* pdb = m_mapDBs[DB_TASK_RECORD];

    DataRecordList taskDetailsRecordList;
    for(auto iter = recordList.cbegin(); iter != recordList.cend(); ++iter)
    {
        DataRecord dbRecord;

        dbRecord.setData(UPGRADE_TASK_ID, (*iter).taskID);
        dbRecord.setData(UPGRADE_DEVICENAME, (*iter).deviceName);
        dbRecord.setData(UPGRADE_STATUS, (*iter).upgradeStatus);

        taskDetailsRecordList.append(dbRecord);
    }

    return pdb->addRecords(taskDetailsRecordList, UPGRADE_TASK_DETAILS_TABLE);
}

bool DBServer::getUpgradeTaskRecord(QList<UpgradeTaskRecord> &recordList, const QString &taskID)
{
    TaskRecordDB* pdb = qobject_cast<TaskRecordDB*>(m_mapDBs[DB_TASK_RECORD]);
    return pdb->getUpgradeTaskRecord(recordList, taskID);
}

bool DBServer::delUpgradeTaskRecord(const QList<UpgradeTaskRecord> &recordList)
{
    TaskRecordDB* pdb = qobject_cast<TaskRecordDB*>(m_mapDBs[DB_TASK_RECORD]);
    return pdb->delUpgradeTaskRecord(recordList);
}

bool DBServer::getUpgradeTaskDetailsRecord(const QString &taskID, QList<UpgradeTaskDetails> &recordList)
{
    TaskRecordDB* pdb = qobject_cast<TaskRecordDB*>(m_mapDBs[DB_TASK_RECORD]);
    return pdb->getUpgradeTaskDetailsRecord(taskID, recordList);
}

bool DBServer::updateUpgradeTaskStatusInfo(const QString &strTaskID, const QString &strDeviceName, const qint16 &taskStatus)
{
    TaskRecordDB* pdb = qobject_cast<TaskRecordDB*>(m_mapDBs[DB_TASK_RECORD]);
    return pdb->updateUpgradeTaskDetailsStatus(strTaskID, strDeviceName, taskStatus);
}

bool DBServer::delUpgradeTaskDetailsRecord(const QList<UpgradeTaskRecord> &recordList)
{
    TaskRecordDB* pdb = qobject_cast<TaskRecordDB*>(m_mapDBs[DB_TASK_RECORD]);
    return pdb->delUpgradeTaskDetailsRecord(recordList);
}

bool DBServer::getPointTrendData(const QString& pointId,
                                const QDateTime& startTime,
                                const QDateTime& endTime,
                                QVector<TrendDataPoint>& trendDataList)
{
    logInfo("getPointTrendData start, pointId: ") << pointId;

    //参数验证
    if(pointId.isEmpty())
    {
        logError("getPointTrendData: pointId is empty");
        return false;
    }

    if(!startTime.isValid() || !endTime.isValid())
    {
        logError("getPointTrendData: invalid time range");
        return false;
    }

    if(startTime > endTime)
    {
        logError("getPointTrendData: startTime is greater than endTime");
        return false;
    }

    //获取测点对应的通道类型
    const QList<ADUChannelType> channelTypeList = m_configService->getChannelTypeListFromPoint(pointId);
    if (channelTypeList.isEmpty())
    {
        logError("get channel type list from point failed, pointId: ") << pointId;
        return false;
    }

    //排除ae,tev,uhf,hfct,tevprps
    const QSet<ADUChannelType> channelTypeSet{CHANNEL_AE, CHANNEL_TEV, CHANNEL_UHF, CHANNEL_HFCT, CHANNEL_TEVPRPS};
    if(channelTypeSet.contains(channelTypeList.first()))
    {
        return false;
    }

    //获取通道类型对应的数据库类型
    const DataBaseServiceType dbType = getDatabaseType(channelTypeList.first());
    if(DB_INVALID == dbType)
    {
        logError("get database type from channel type failed, channelType: ") << channelTypeList.first();
        return false;
    }

    if(!m_mapDBs.contains(dbType))
    {
        logError("m_mapDBs not contains dbType: ") << dbType;
        return false;
    }

    SqliteDB* pdb = static_cast<SqliteDB*>(m_mapDBs[dbType]);
    if(!pdb)
    {
        logError("pdb is null, dbType: ") << dbType;
        return false;
    }

    // 根据通道类型确定查询字段
    QList<ItemKeyWrapper> fields;
    ADUChannelType channelType = channelTypeList.first();

    switch(channelType)
    {
    case CHANNEL_TEMPERATURE:
        fields << IK_RECORDTIME_T << TEMP_DATA;
        break;
    case CHANNEL_HUMIDITY:
        fields << IK_RECORDTIME_T << HUMI_DATA;
        break;
    default:
        logError("unsupported channel type: ") << channelType;
        return false;
    }

    // 使用queryRecordsWithoutPagination获取数据（正序排列）
    DataRecordList records;
    bool success = pdb->queryRecordsWithoutPagination(pointId, fields, startTime, endTime,
                                                     SqliteDB::TimeSortOrder::Ascending, records);
    if(!success)
    {
        logError("queryRecordsWithoutPagination failed, pointId: ") << pointId;
        return false;
    }

    // 清空输出列表
    trendDataList.clear();
    trendDataList.reserve(records.size());

    // 解析数据记录并转换为TrendDataPoint
    for(const DataRecord& record : records)
    {
        // 获取时间戳（INTEGER类型）
        QVariant timeVariant = record.value(IK_RECORDTIME_T);
        if(!timeVariant.isValid())
        {
            logWarnning("invalid timestamp in record");
            continue;
        }

        qint64 timestamp = timeVariant.toLongLong();
        QDateTime recordTime = QDateTime::fromTime_t(timestamp);

        // 获取数据字段（BLOB类型，JSON格式）
        ItemKeyWrapper dataKey = (channelType == CHANNEL_TEMPERATURE) ? TEMP_DATA : HUMI_DATA;
        QVariant dataVariant = record.value(dataKey);
        if(!dataVariant.isValid())
        {
            logWarnning("invalid data in record");
            continue;
        }

        // 解析JSON数据
        QByteArray jsonData = dataVariant.toByteArray();
        QJsonParseError parseError;
        QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonData, &parseError);

        if(parseError.error != QJsonParseError::NoError)
        {
            logWarnning("failed to parse JSON data: ") << parseError.errorString();
            continue;
        }

        QJsonObject jsonObj = jsonDoc.object();
        if(!jsonObj.contains("value"))
        {
            logWarnning("JSON object does not contain 'value' field");
            continue;
        }

        // 提取value字段的数值
        QJsonValue valueJson = jsonObj["value"];
        float value = 0.0f;

        if(valueJson.isDouble())
        {
            value = static_cast<float>(valueJson.toDouble());
        }
        else if(valueJson.isNull())
        {
            value = 0.0f; // null值处理为0
        }
        else
        {
            logWarnning("invalid value type in JSON");
            continue;
        }

        // 创建TrendDataPoint并添加到列表
        TrendDataPoint point(recordTime, value);
        trendDataList.append(point);

    }

    logInfo("getPointTrendData success, got ") << trendDataList.size() << " trend data points";
    return true;
}

bool DBServer::getTrendData(const QString &pointId, const QDateTime &startTime, const QDateTime &endTime, DBServer::TrendDataType dataType, QVector<DBServer::TrendDataPoint> &trendDataList)
{
    logInfo("getTrendData start, pointId: ") << pointId << ", dataType: " << static_cast<int>(dataType);

    // 参数验证
    if(pointId.isEmpty())
    {
        logError("getTrendData: pointId is empty");
        return false;
    }

    if(!startTime.isValid() || !endTime.isValid())
    {
        logError("getTrendData: invalid time range");
        return false;
    }

    if(startTime > endTime)
    {
        logError("getTrendData: startTime is greater than endTime");
        return false;
    }

    if(dataType == TrendDataType::Invalid)
    {
        logError("getTrendData: invalid data type");
        return false;
    }

    // 获取字段配置
    ADUChannelType channelType;
    ItemKeyWrapper fieldKey;
    bool isJsonFormat;
    QString jsonFieldName;

    if(!getFieldConfigByDataType(dataType, channelType, fieldKey, isJsonFormat, jsonFieldName))
    {
        logError("getTrendData: unsupported data type: ") << static_cast<int>(dataType);
        return false;
    }

    // 验证测点是否支持该数据类型
//    if(!validatePointDataType(pointId, dataType))
//    {
//        logError("getTrendData: point does not support this data type, pointId: ") << pointId;
//        return false;
//    }

    // 获取通道类型对应的数据库类型
    const DataBaseServiceType dbType = getDatabaseType(channelType);
    if(DB_INVALID == dbType)
    {
        logError("get database type from channel type failed, channelType: ") << channelType;
        return false;
    }

    if(!m_mapDBs.contains(dbType))
    {
        logError("m_mapDBs not contains dbType: ") << dbType;
        return false;
    }

    SqliteDB* pdb = static_cast<SqliteDB*>(m_mapDBs[dbType]);
    if(!pdb)
    {
        logError("pdb is null, dbType: ") << dbType;
        return false;
    }

    // 构建查询字段
    QList<ItemKeyWrapper> fields;
    fields << IK_RECORDTIME_T << fieldKey;

    // 查询数据
    DataRecordList records;
    bool success = pdb->queryRecordsWithoutPagination(pointId, fields, startTime, endTime,
                                                      SqliteDB::TimeSortOrder::Ascending, records);
    if(!success)
    {
        logError("queryRecordsWithoutPagination failed, pointId: ") << pointId;
        return false;
    }

    // 清空输出列表
    trendDataList.clear();
    trendDataList.reserve(records.size());

    // 解析数据记录并转换为TrendDataPoint
    for(const DataRecord& record : records)
    {
        // 获取时间戳
        QVariant timeVariant = record.value(IK_RECORDTIME_T);
        if(!timeVariant.isValid())
        {
            logWarnning("invalid timestamp in record");
            continue;
        }

        qint64 timestamp = timeVariant.toLongLong();
        QDateTime recordTime = QDateTime::fromTime_t(timestamp);

        float value = 0.0f;

        if(isJsonFormat)
        {
            // JSON格式数据处理
            QVariant dataVariant = record.value(fieldKey);
            if(!dataVariant.isValid())
            {
                logWarnning("invalid data in record");
                continue;
            }

            // 解析JSON数据
            QByteArray jsonData = dataVariant.toByteArray();
            QJsonParseError parseError;
            QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonData, &parseError);

            if(parseError.error != QJsonParseError::NoError)
            {
                logWarnning("failed to parse JSON data: ") << parseError.errorString();
                continue;
            }

            QJsonObject jsonObj = jsonDoc.object();
            if(!jsonObj.contains(jsonFieldName))
            {
                logWarnning("JSON object does not contain field: ") << jsonFieldName;
                continue;
            }

            QJsonValue valueJson = jsonObj[jsonFieldName];
            if(valueJson.isDouble())
            {
                value = static_cast<float>(valueJson.toDouble());
            }
            else if(valueJson.isNull())
            {
                value = 0.0f;
            }
            else
            {
                logWarnning("invalid value type in JSON");
                continue;
            }
        }
        else
        {
            // 直接数值格式处理
            QVariant dataVariant = record.value(fieldKey);
            if(!dataVariant.isValid())
            {
                logWarnning("invalid data in record");
                continue;
            }

            value = dataVariant.toFloat();
        }

        // 创建TrendDataPoint并添加到列表
        TrendDataPoint point(recordTime, value);
        trendDataList.append(point);
    }

    logInfo("getTrendData success, got ") << trendDataList.size() << " trend data points";
    return true;
}

} // namesapce storage
