#include "others.h"
#include "commandfuns.h"

Others::Others()
{

}

/************************************************
* 函数名:  getDeviceNameList
* 输入参数:  NULL
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取设备名称列表
************************************************/
void Others::getDeviceNameList(int iType)
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree =  configService.stationNode();
    for (int i = 0;i < deviceTree.devices.size(); i++)
    {
        QJsonObject jsonDevice;
        const DeviceNode &device = deviceTree.devices.at(i);
        jsonDevice.insert(STR_ID, device.strDeviceGUID);
        jsonDevice.insert(STR_NAME, device.strName);
        jsonDevice.insert(STR_TYPE, iType);
        jsonDevice.insert(STR_IS_PARENT, !device.testPoints.isEmpty());
        append(jsonDevice);
    }
}

/************************************************
* 函数名:  GetPointNameList
* 输入参数:  ucDeviceID -- 设备id
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取指定设备下的测点列表
************************************************/
void Others::GetPointNameList(const QString &strDeviceCode, int iType)
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree =  configService.stationNode();
    for (int i = 0;i < deviceTree.devices.size(); i++)
    {
        if (strDeviceCode == deviceTree.devices.at(i).strDeviceGUID)
        {
            const DeviceNode &deviceNode = deviceTree.devices.at(i);
            for (int j = 0; j < deviceNode.testPoints.size(); j++)
            {
                const TestPointInfo &testPoint = deviceNode.testPoints.at(j);
                QJsonObject jsonPoint;
                jsonPoint.insert(STR_POINT_NAME, testPoint.strName);
                jsonPoint.insert(STR_NAME, testPoint.strOutName);
                jsonPoint.insert(STR_POINT_ID, testPoint.strPointGUID);
                jsonPoint.insert(STR_ID, testPoint.strPointGUID);
                jsonPoint.insert(STR_TYPE, iType);
                jsonPoint.insert(STR_IS_PARENT, !testPoint.ConnectionInfo.isEmpty());

                append(jsonPoint);
            }
        }
    }
}

/************************************************
* 函数名:  GetPointNameListApp
* 输入参数:  ucDeviceID -- 设备id
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取指定设备下的测点列表
************************************************/
void Others::GetPointNameListApp(const QString &strDeviceCode, int iType)
{
    ConfigService &configService = ConfigService::instance();
    
    const QList<DeviceNode> &deviceList = configService.stationNode().devices;
    
    //查找目标设备
    auto deviceIt = std::find_if(deviceList.begin(), deviceList.end(),
        [&strDeviceCode](const DeviceNode& device) {
            return device.strDeviceGUID == strDeviceCode;
        });
        
    if (deviceIt == deviceList.end()) {
        return;  // 未找到设备,直接返回
    }

    auto createADUChannelObj = [&configService](const PointConnectionInfo &connInfo) -> QJsonObject {
        QJsonObject aduChannelObj;
        aduChannelObj.insert(STR_ADU_ID, connInfo.strID);
    
        ADUType eADUType;
        configService.getADUTypeFromID(connInfo.strID, eADUType);
        aduChannelObj.insert(STR_ADU_TYPE, configService.getADUTypName(eADUType));
        
        aduChannelObj.insert(STR_CHANNEL_INDEX, connInfo.unID);
        aduChannelObj.insert(STR_CHANNEL_TYPE, configService.getChanTypName(connInfo.etype));
        return aduChannelObj;
    };
    
    // 遍历测点
    for (const auto& pointTmp : deviceIt->testPoints)
    {
        QJsonObject pointInfoObj;
        pointInfoObj.insert(STR_POINT_NAME, pointTmp.strName);
        pointInfoObj.insert(STR_NAME, pointTmp.strOutName);
        pointInfoObj.insert(STR_POINT_ID, pointTmp.strPointGUID);
        pointInfoObj.insert(STR_ID, pointTmp.strPointGUID);
        pointInfoObj.insert(STR_TYPE, iType);
        pointInfoObj.insert(STR_IS_PARENT, !pointTmp.ConnectionInfo.isEmpty());
        //通道信息
        QJsonArray aduChannels;
        for (const auto& connInfo : pointTmp.ConnectionInfo) {
            aduChannels.append(createADUChannelObj(connInfo));
        }
        pointInfoObj.insert(STR_ADU_CHANNELS, aduChannels);
        
        append(pointInfoObj);
    }
}

/************************************************
* 函数名:  GetPointNameList
* 输入参数:  ucDeviceID -- 设备id
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取指定设备下的测点列表
************************************************/
void Others::GetPointNameList(void)
{
    ConfigService &configService = ConfigService::instance();
    const StationNode &deviceTree =  configService.stationNode();
    for (int i = 0;i < deviceTree.devices.size(); i++)
    {
        const DeviceNode &deviceNode = deviceTree.devices.at(i);
        for (int j = 0; j < deviceNode.testPoints.size(); j++)
        {
            const TestPointInfo &testPoint = deviceNode.testPoints.at(j);
            QJsonObject jsonPoint;
            jsonPoint.insert(STR_POINT_NAME, testPoint.strName);

            append(jsonPoint);
        }
    }
}

/************************************************
* 函数名:  GetPointNameList
* 输入参数:  strAUDType -- 前端类型
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取测点列表
************************************************/
void Others::getADUID(const QString &strAUDType)
{
    ADUType eADUType =  ConfigService::instance().getADUTypEnum(strAUDType);
    ConfigService &configService = ConfigService::instance();
    const QList<ADUUnitInfo> &ADUList =  configService.ADUList();
    for (int j = 0; j < ADUList.size(); j++)
    {
        const ADUUnitInfo &adu = ADUList.at(j);
        if (adu.eType == eADUType)
        {
            append(adu.strID);
        }
    }
}

/************************************************
 * 函数名:  GetADUType
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 获取前端类型
 ************************************************/
void Others::GetADUType(void)
{
    int index = 0;
    QList<QString> aduSwitch = ConfigService::instance().getSwitchADU();
    for(auto i = 0; i < aduSwitch.size(); i++)
    {

        ADUType aduType = ConfigService::instance().getADUTypEnum(aduSwitch[i]);
        if(ConfigService::instance().getISADUType("", aduType))
        {
            QJsonObject json;
            json.insert(STR_KEY, index);
            json.insert(STR_VALUE, aduSwitch[i]);
            append(json);
        }
        else if(ADU_PD_THREE == aduType || ADU_PD_FIVE == aduType)
        {
            QJsonObject json;
            json.insert(STR_KEY, index);
            json.insert(STR_VALUE, aduSwitch[i]);
            append(json);
        }
    }
}
/************************************************
 * 函数名:  GetSyncDataADUType
 * 输入参数:  request -- http请求
 *      response -- 响应
 * 输出参数:  NULL
 * 返回值:  NULL
 * 功能: 获取数据同步前端类型
 ************************************************/
void Others::GetSyncDataADUType(void)
{
    QList<QString> aduSwitch = ConfigService::instance().getSwitchADU();
    for(auto i = 0; i < aduSwitch.size(); i++)
    {
        if(aduSwitch[i].contains("_IS"))
        {
            QJsonObject json;
            json.insert(STR_KEY, i);
            json.insert(STR_VALUE, aduSwitch[i]);
            append(json);
        }
    }
}

void Others::getFindGroupNoAduType()
{
    QList<QString> aduSwitch = ConfigService::instance().getSwitchADU();
    for(auto i = 0; i < aduSwitch.size(); ++i)
    {
        if(aduSwitch[i] == "FLOOD_IS")
        {
            continue;
        }
        if(aduSwitch[i].contains("_IS"))
        {
            QJsonObject json;
            json.insert(STR_KEY, i);
            json.insert(STR_VALUE, aduSwitch[i]);
            append(json);
        }
    }
}

/************************************************
 * 函数名:  getaduTypeList
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  前端类型列表
 * 功能:   前端类型列表
 ************************************************/
void Others::getaduTypeList(void)
{
    QList<QString> aduSwitch = ConfigService::instance().getSwitchADU();
    for(auto i = 0; i < aduSwitch.size(); i++)
    {
        append(aduSwitch[i]);
    }

/*#ifdef SERVER_PD_THREE
    append(STR_PD_THREE);
#endif

#ifdef SERVER_PD_FIVE
    append(STR_PD_FIVE);
#endif

#ifdef SERVER_IS_OLD_PROTOCOL
    append(STR_IS);
#endif

#ifdef SERVER_MECH
    append(STR_MECH);
#endif

#ifdef SERVER_IS_UHF
    append(STR_UHF_IS);
#endif

#ifdef SERVER_IS_UFCT
    append(STR_HFCT_IS);
#endif

#ifdef SERVER_IS_PD
    append(STR_PD_IS);
#endif

#ifdef SERVER_IS_ENV
    append(STR_ENV_IS);
#endif

#ifdef SERVER_IS_ARRESTER
    append(STR_ARRESTER_U_IS);
    append(STR_ARRESTER_I_IS);
#endif

#ifdef SERVER_IS_VIBRATION
    append(STR_VIBRATION_IS);
#endif

#ifdef SERVER_IS_MECH
    append(STR_MECH_IS);
#endif

#ifdef SERVER_433PD_485MECH
    append(STR_MECH_IS);
#endif

#ifdef SERVER_IS_AE_GIS
    append(STR_GIS_AE_IS);
#endif

#ifdef SERVER_IS_FLOOD
    append(STR_FLOOD_IS);
#endif

#ifdef SERVER_IS_AE_TRANSFORMER
    append(STR_TRANSFORMER_AE_IS);
#endif

#ifdef SERVER_IS_TEMP_HUM
    append(STR_TEMP_HUM_IS);
#endif

#ifdef VAISALA_DTP145
    append(STR_VAISALADTP145);
#endif

#ifdef WIKA_GDT_20
    append(STR_WIKA_GDT20);
#endif

#ifdef WIKA_GDHT_20
    append(STR_WIKA_GDHT20);
#endif

#ifdef SH_QIUQI_SC75D_SF6
    append(STR_SHQIUQI_SC75D_SF6);
#endif

#ifdef SERVER_IS_LOWTEN
    append(STR_LOWTEN_IS);
#endif
#ifdef SERVER_IS_OPTICALTEMP
    append(STR_OPTICAL_TEMP_IS);
#endif
#ifdef SERVER_IS_LIGHT
    append(STR_LIGHT_IS);
#endif
#ifdef SERVER_IS_FAN
    append(STR_FAN_IS);
#endif
#ifdef SERVER_THIRD_ENVIHUMID
    append(STR_THIRD_ENVI_HUMID);
#endif
#ifdef SERVER_IS_NOSIE_SH
    append(STR_NOISE_IS);
#endif
#ifdef SERVER_IS_FIREWORKS
    append(STR_FIREWORKS_ALARM_IS);
#endif
#ifdef SERVER_IS_WATER
    append(STR_WATER_IS);
#endif
#ifdef SERVER_IS_LIQUIDLEVEL
    append(STR_LIQUID_LEVEL_IS);
#endif
#ifdef SERVER_IS_AIRCONDITION
    append(STR_AIR_CONDITIONER_IS);
#endif
#ifdef SERVER_IS_MODBUSSF6
    append(STR_SF6_IS);
#endif
#ifdef SERVER_IS_LOWTENSVG
    append(STR_LOWTENSVG_IS);
#endif
#ifdef SERVER_IS_WIRELESSNOISE
    append(STR_WIRELESSNOISE_IS);
#endif
#ifdef SERVER_IS_WIRELESSREDTEMP
    append(STR_WIRELESSREDTEMP_IS);
#endif
#ifdef SERVER_IS_WIRELESSTEMPHUMID
    append(STR_WIRELESSTEMPHUMID_IS);
#endif
#ifdef SERVER_IS_WIRELESSWATER
    append(STR_WIRELESSWATER_IS);
#endif
#ifdef SERVER_IS_WIRELESSLIQUIDLEVEL
    append(STR_WIRELESSLIQUIDLEVEL_IS);
#endif
#ifdef SERVER_IS_WIRELESSFIREWORKS
    append(STR_WIRELESSFIREWORKS_IS);
#endif

#ifdef OTHER_MONITOR
#ifdef TEMPXP_OUT
    append(STR_TEMPXP_OUT);
#endif
#ifdef TEMPFC_OUT
    append(STR_TEMPFC_OUT);
#endif
#ifdef SF6YN_OUT
    append(STR_SF6YN_OUT);
#endif
#ifdef SMOKERK_OUT
    append(STR_SMOKERK_OUT);
#endif
#ifdef TEMPKY_OUT
    append(STR_TEMPKY_OUT);
#endif
#ifdef TEMPSB_OUT
    append(STR_TEMPSB_OUT);
#endif
#ifdef TEMP_HUM_JDRK_OUT
    append(STR_TEMP_HUM_JDRK_OUT);
#endif
#endif*/

}

/************************************************
 * 函数名:  getVolLevelList
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  电压等级列表
 * 功能:   获取电压等级列表
 ************************************************/
void Others::getVolLevelList(void)
{
    QList<QString> voltageLevelNames = ConfigService::instance().listVoltageLevelName();
    for (int i = 0; i < voltageLevelNames.size(); i++)
    {
        append(voltageLevelNames.at(i));
    }
}

/************************************************
 * 函数名:  getDeviceTypeList
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  一次设备类型列表
 * 功能:   获取一次设备类型列表
 ************************************************/
void Others::getDeviceTypeList(void)
{
    QList<QString> deviceTypeNames = ConfigService::instance().listDeviceTypeName();

    append(deviceTypeNames.at(DEVICE_TRANSFORMER - 1));//变压器
    append(deviceTypeNames.at(DEVICE_BREAK - 1));//断路器
    append(deviceTypeNames.at(DEVICE_DISCONNECTOR - 1));//隔离开关
    append(deviceTypeNames.at(DEVICE_ISOLATOR - 1));//刀闸
    append(deviceTypeNames.at(DEVICE_ARRESTER - 1));//避雷器
    append(deviceTypeNames.at(DEVICE_PT - 1));//电压互感器
    append(deviceTypeNames.at(DEVICE_CT - 1));//电流互感器
    append(deviceTypeNames.at(DEVICE_BUSBAR - 1));//母线
//    append(deviceTypeNames.at(DEVICE_CIRCUIT - 1));//母联
    append(deviceTypeNames.at(DEVICE_SWITCHGEAR - 1));//开关柜
    append(deviceTypeNames.at(DEVICE_POWER_CABLE - 1));//电力电缆
//    append(deviceTypeNames.at(DEVICE_LIGHTNING_ROD - 1));//避雷针
//    append(deviceTypeNames.at(DEVICE_WALL_BUSHING - 1));//穿墙套管
    append(deviceTypeNames.at(DEVICE_REACTOR - 1));//电抗器
//    append(deviceTypeNames.at(DEVICE_ELECTRIC_CONDUCTOR - 1));//电力导线（导流排）
    append(deviceTypeNames.at(DEVICE_POWER_CAPACITOR - 1));//电力电容器
//    append(deviceTypeNames.at(DEVICE_DISCHARGE_COIL - 1));//放电线圈
    append(deviceTypeNames.at(DEVICE_lOAD_SWITCH - 1));//负荷开关
//    append(deviceTypeNames.at(DEVICE_GROUNDING_TRANSFORMER - 1));//接地变
//    append(deviceTypeNames.at(DEVICE_GROUNDING_RESISTANCE - 1));//接地电阻
//    append(deviceTypeNames.at(DEVICE_GROUNDING_GRID - 1));//接地网
//    append(deviceTypeNames.at(DEVICE_COMBINED_FILTER - 1));//结合滤波器
    append(deviceTypeNames.at(DEVICE_INSULATOR - 1));//绝缘子
    append(deviceTypeNames.at(DEVICE_COUPLING_CAPACITOR - 1));//耦合电容器
    append(deviceTypeNames.at(DEVICE_CABINET - 1));//屏柜
    append(deviceTypeNames.at(DEVICE_OTHER - 1));//其他
    append(deviceTypeNames.at(DEVICE_FUSE - 1));//熔断器
    append(deviceTypeNames.at(DEVICE_USING_TRANSFORMER - 1));//所用变
//    append(deviceTypeNames.at(DEVICE_ARC_SUPPRESSION_DEVICE - 1));//消弧装置
    append(deviceTypeNames.at(DEVICE_MAIN_TRANSFORMER - 1));//主变压器
//    append(deviceTypeNames.at(DEVICE_WAVE_TRAP - 1));//阻波器
    append(deviceTypeNames.at(DEVICE_COMBINED_ELECTRIC_APPLICANCE - 1));//组合电器
//    append(deviceTypeNames.at(DEVICE_COMBINED_TRANSFORMER - 1));//组合互感器
}

/************************************************
 * 函数名:  getLinkTypeList
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  通讯链路类型列表
 * 功能:   获取通讯链路类型列表
 ************************************************/
void Others::getLinkTypeList(void)
{
    //append("Invalid");
    append("LoRa");
    append("Modbus485");
    append("Modbus_Over_Tcp");
    append("IO");
}

/************************************************
 * 函数名:  getChannelTypeList
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  通道类型列表
 * 功能:   获取通道类型列表
 ************************************************/
void Others::getChannelTypeList(void)
{
    append(STR_UNKNOW);
    append(STR_UHF);
    append(STR_TEVPRPS);
    append(STR_HFCT);
    append(STR_TEV);
    append(STR_AE);
    append(STR_TEMP);
    append(STR_VAISALADTP145);
    append(STR_WIKA_GDT20);
    append(STR_WIKA_GDHT20);
    append(STR_SHQIUQI_SC75D_SF6);
//    append("CH_Ultrasonic");
//    append("CH_Arrester_I");
//    append("CH_Arrester_U");
//    append("CH_Vibration");
//    append("CH_MECH");
//    append("CH_Temperature");
//    append("CH_Humidity");
//    append("CH_Air_Pressure");
//    append("CH_Rain_Fall");
//    append("CH_Rain_Intensity");
//    append("CH_Light_Intensity");
//    append("CH_Noisy");
//    append("CH_Wind_Speed");
}

void Others::getRS485ComPortList()
{
    append(STR_RS485_PORT1);
    append(STR_RS485_PORT2);
}

void Others::getADUWorkModeList()
{
    append(STR_ADU_NORMAL_MODE);
    append(STR_ADU_LOWER_POWER_MODE);
    append(STR_ADU_MONIOR_MODE);
}
void Others::getLowerPowerWakeIpSpace()
{
    append(3);
    append(5);
    append(10);
    append(15);
    append(20);
    append(30);
    append(60);
}

/************************************************
 * 函数名:  GetADUList
 * 输入参数:  NULL
 * 输出参数:  NULL
 * 返回值:  前端列表
 * 功能:   获取前端列表
 ************************************************/
void Others::getADUList(void)
{
    QList<ADUUnitInfo> adus = ConfigService::instance().ADUList();
    for (int i = 0; i < ADU_TYPE_NUM; i++)
    {
        QJsonObject jsonADUType;
        jsonADUType.insert(STR_ADU_TYPE_INDEX, i);

        QString strADU = ConfigService::instance().getADUTypName((ADUType)i);
        jsonADUType.insert(STR_ADU_TYPE, strADU);
        QJsonArray jsonADUs;
        for (int j = 0; j < adus.size(); j++)
        {
            if (adus.at(j).eType == (ADUType)i)
            {
                QJsonObject jsonADU;
                jsonADU.insert(STR_ADU_NAME, adus.at(j).strName);
                jsonADU.insert(STR_ADU_ID, adus.at(j).strID);
                jsonADUs.append(jsonADU);
            }
        }
        if (!jsonADUs.isEmpty())
        {
            jsonADUType.insert(STR_ADU_ITEMS, jsonADUs);
            append(jsonADUType);
        }
    }
}


/************************************************
 * 功能:   设置告警（安全测评）
 ************************************************/
//void Others::setSafeAlarm( QList<PDSSAFE_W::WarnInfo>listInfo)
//{
//    infoLog() << "xuwei setSafeAlarm = " << listInfo.size();
//    QJsonObject jsonInfo;
//    QJsonArray jsonArrAlarm;
//    for (int i = 0; i < listInfo.size(); i++)
//    {
//        QJsonObject jsonAlarm;
//        jsonAlarm.insert(STR_POINT_NAME, listInfo.at(i).pointName);
//        jsonAlarm.insert(STR_ADU_ID, listInfo.at(i).aduID.toString());
//        jsonAlarm.insert(STR_CHANNEL_TYPE, listInfo.at(i).channelType);
//        jsonAlarm.insert("warnLevel", listInfo.at(i).warnLevel);
//        jsonAlarm.insert("warnState", listInfo.at(i).warnState);
//        jsonAlarm.insert("warnValue", listInfo.at(i).warnValue.toFloat());
//        jsonAlarm.insert("alarmId", (int)listInfo.at(i).index);
//        jsonAlarm.insert("alarmTime", listInfo.at(i).dateTime.toString("yyyy-MM-dd hh:mm:ss"));
//        jsonArrAlarm.append(jsonAlarm);
//    }
//    jsonInfo.insert("rows", jsonArrAlarm);
//    jsonInfo.insert("total", listInfo.size());
//    append(jsonInfo);
//}

void Others::getAlarmConfigTreeList(void)
{
    QList<int> listTaskGroupNum;
    QList<AlarmConfigInfo> listAlarmConfigInfos = ConfigService::instance().alarmConfigInfo();

    for( int i = 0; i < listAlarmConfigInfos.size(); i++ )
    {
        int iTaskGroup = listAlarmConfigInfos.at(i).iTaskGroup;

        if(!listTaskGroupNum.contains(iTaskGroup))
        {
            listTaskGroupNum.append(iTaskGroup);

            QJsonObject jsonTaskGroupLayer;
            jsonTaskGroupLayer.insert( STR_SF6_ALARM_ID, iTaskGroup );
            jsonTaskGroupLayer.insert( STR_SF6_ALARM_NAME, iTaskGroup );
            jsonTaskGroupLayer.insert( STR_SF6_ALARM_PARENT_ID, -1 );
            jsonTaskGroupLayer.insert( STR_SF6_ALARM_ARRT_TYPE, 0 );

            append(jsonTaskGroupLayer);
        }

        ADUChannelType eChannelType = listAlarmConfigInfos.at(i).eChannelType;

        QJsonObject jsonChannelTypeLayer;

        QString strChanName =  ConfigService::instance().getChanTypName(eChannelType);
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_ID, strChanName);
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_NAME, strChanName);
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_PARENT_ID, iTaskGroup );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_ARRT_TYPE, 1 );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_THRESHOLD, listAlarmConfigInfos.at(i).fAlarmThreshold );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_RECOVERY_THRESHOLD, listAlarmConfigInfos.at(i).fAlarmRecoveryThershold );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_CHANNEL, ConfigService::instance().getAlarmChannelStr(listAlarmConfigInfos.at(i).eAlarmChannel));
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_EXTERNAL_IO_SN, listAlarmConfigInfos.at(i).strAlarmExternalIOSN );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_EXTERNAL_IO_CHANNEL, listAlarmConfigInfos.at(i).iAlarmExternalIOChannel );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_WIRING_MODE, ConfigService::instance().getAlarmWiringModeStr(listAlarmConfigInfos.at(i).eWiringMode) );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_MODE, ConfigService::instance().getAlarmModeStr(listAlarmConfigInfos.at(i).eAlarmMode) );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_TIME, listAlarmConfigInfos.at(i).iAlarmTime );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_INTERVAL, listAlarmConfigInfos.at(i).iAlarmInterval );
        jsonChannelTypeLayer.insert( STR_SF6_ALARM_DURATION, listAlarmConfigInfos.at(i).iAlarmDuration );

        append(jsonChannelTypeLayer);
    }
}

/************************************************
* 函数名:  channelList
* 输入参数:  strADUID -- 前端id
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取通道列表数据
************************************************/
void Others::getChannelList(const QString &strADUID)
{
    ADUUnitInfo adu;
    if (ConfigService::instance().getADU(strADUID,adu))
    {
        for (int i = 0; i < adu.Channels.size(); i++)
        {
            QJsonObject jsonChannel;
            QString chanName = ConfigService::instance().getChanTypName(adu.Channels.at(i).etype);
            jsonChannel.insert(STR_CHANNEL_TYPE, chanName);
            jsonChannel.insert(STR_CHANNEL_INDEX, adu.Channels.at(i).unID);
            jsonChannel.insert(STR_CHANNEL_NAME, adu.Channels.at(i).strName);
            jsonChannel.insert(STR_ID, adu.Channels.at(i).unID);
            jsonChannel.insert(STR_NAME, adu.Channels.at(i).strName);
            jsonChannel.insert(STR_TYPE, 4);
            append(jsonChannel);
        }
    }
}

/************************************************
* 函数名:  getChannelListFromPoint
* 输入参数:  strADUID -- 测点id
* 输出参数:  NULL
* 返回值:  NULL
* 功能:  获取通道列表数据
************************************************/
void Others::getChannelListFromPoint(const QString &strPointID)
{
    ADUChannelInfo channel;
    TestPointInfo stPoint;

    if (ConfigService::instance().getTestPoint(strPointID,stPoint))
    {
        for (int i = 0 ; i < stPoint.ConnectionInfo.size(); i++)
        {
            if (ConfigService::instance().getChannelInfo(stPoint.ConnectionInfo.at(i).strID,stPoint.ConnectionInfo.at(i).unID, channel))
            {
                QJsonObject jsonChannel;
                jsonChannel.insert(STR_ID, channel.unID);
                jsonChannel.insert(STR_NAME, channel.strName);
                jsonChannel.insert(STR_TYPE, 4);
                append(jsonChannel);
            }
        }
    }
}

void Others::getHistoryTreeChannelInfoFromPoint(const QString &strPointID)
{
    ConfigService &config = ConfigService::instance();

    TestPointInfo stPoint;

    if (config.getTestPoint(strPointID,stPoint))
    {
        QSet<int> chTypes;
        for (int i = 0 ; i < stPoint.ConnectionInfo.size(); i++)
        {
            ADUChannelType eChType = stPoint.ConnectionInfo[i].etype;
            if(!chTypes.contains(eChType))
            {
                chTypes.insert(eChType);
                QJsonObject jsonChannel;
                QString chanName = ConfigService::instance().getChanTypName(eChType);
                jsonChannel.insert(STR_ID, chanName);
                jsonChannel.insert(STR_NAME, chanName);
                jsonChannel.insert(STR_TYPE, 4);
                append(jsonChannel);
            }
        }
    }
}
