#include "websocket.h"
#include "monitorservice.h"
#include "moisturecontroller.h"
#include "gpio.h"
#include "systemsettings.h"
#include "usermanager.h"
#include "statemonitor.h"
#include "log.h"
#include "auditmanager.h"
#include "dataexport/dataexportmanager.h"
#include "outwardservice.h"
#include <QFile>

#define WEBSOCKET_PRINT  //模块打印开关

#define WEBSOCKET_TIMER   100//定时器事件间隔 100ms
#define LIGHTCHANGE_TIMER  1000 //灯光变化定时器
#define SYSTEMINFO_TIMER   5000 //系统信息推送
#define ADU_LOG_SAVE_PATH  QStringLiteral("/media/backup/download/ADULogFile")   //传感器日志存放路径
#define MONITOR_TABLE_UPDATE_INTERVAL 5000 //主机数据表更新间隔

/*************************************************
函数名： WebSocket(QObject *parent = 0)
输入参数： parent：父指针
        port -- 端口
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
WebSocket::WebSocket(quint16 port, QObject *parent)
    :QObject(parent)
    ,m_pThread(NULL)
    ,m_mutexAffair(QMutex::Recursive)
    ,m_webSocket(new QWebSocketServer(strSocketName,
                                      QWebSocketServer::NonSecureMode, this))
    ,m_IsPushLogInfo(true)
    ,m_PushLogInfoLevel(0)
    ,m_iTimer(-1)
    ,m_iConnectionADUTiemout(-1)
    ,m_iTimerPushMonitorTable(-1)
    ,iAffairCount(0)
    ,m_usLightInterval(0)
    ,m_usSystemInfoInterval(0)
{
    init(port);
}

/*************************************************
函数名： timerEvent(QTimerEvent *event)
输入参数： event：定时器事件
输出参数： NULL
返回值： NULL
功能： 定时器事件处理
*************************************************************/
void WebSocket::timerEvent(QTimerEvent *event)
{
    if (event->timerId() == m_iTimer)
    {
        pushSystemInfo();

        if(MONITOR_TYPE_COLLOCTION_NODE == ConfigService::instance().getHardType())
        {
            changeLEDLight();
        }
#ifdef SERVER_REBOOT_EVERYDAY
        QString strCurentTime = QDateTime::currentDateTime().time().toString("hh:mm:ss");
        QString strTime = "01:45:00";
        if(strTime == strCurentTime)
        {
            system("reboot");
        }
#endif
    }
    if (event->timerId() == m_iTimerPushMonitorTable)
    {
        if (m_bMonitorTableUpdate)
        {
            pushADUConnectionInfo();
            m_bMonitorTableUpdate = false;
        }
    }
    if (event->timerId() == m_iConnectionADUTiemout)
    {
        //onDisconnectionADUTimeOut();
        killTimer(m_iConnectionADUTiemout);
        m_iConnectionADUTiemout = -1;
    }
}

/*************************************************
函数名： pushSystemInfo
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送系统信息（时间，cpu、内存、硬盘）
*************************************************************/
void WebSocket::pushSystemInfo(void)
{
    m_usSystemInfoInterval += WEBSOCKET_TIMER;
    if(m_usSystemInfoInterval > SYSTEMINFO_TIMER)
    {
        m_usSystemInfoInterval = 0;

        static bool s_bCpuOverLoad = false;
        QJsonObject result;
        QJsonObject data;
        bool ret;
        result.insert(STR_NET_WORK,QString::number(ConfigService::instance().getGPRSSignal()));
        result.insert(STR_DISK,QString::number(100 - SystemInfo::getRemainStoragePercent(),'g',3)+"%");
        result.insert(STR_MEMRY,QString::number(SystemInfo::getMemoryUsedPercent(ret),'g',3) + "%");
        // TEST 定位cpu占用过大的原因
        float fCpuPer = SystemInfo::getCpuUsedPercent(ret);
        bool bOverLoad = fCpuPer > 80.0;
        if( bOverLoad != s_bCpuOverLoad)
        {
            PDS_SYS_INFO_LOG("Cpu overload state is changed, current cpu per is : %f", fCpuPer);
            s_bCpuOverLoad = bOverLoad;
        }
        result.insert(STR_CPU,QString::number(fCpuPer,'g',3) + "%");

        result.insert(STR_DATE_TIME, QDateTime::currentDateTime().toString(STR_DATE_TIME_QSTRING_USA) );
        data.insert(STR_PUSH_SYS_INFO,result);
        for (int i = 0; i < m_clients.size(); i++)
        {
            m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(data)).toJson());
        }
    }
}
/*************************************************
函数名： changeLEDLight
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 切换运行指示灯状态
*************************************************************/
void WebSocket::changeLEDLight(void)
{
#ifdef Q_OS_LINUX
    m_usLightInterval += WEBSOCKET_TIMER;
    if(m_usLightInterval >= LIGHTCHANGE_TIMER)
    {
        m_usLightInterval = 0;
        if (m_bLEDLiteState)
        {
            led_ctrl(LED_FAULT, LED_ON);
        }
        else
        {
            led_ctrl(LED_FAULT, LED_OFF);
        }
        m_bLEDLiteState = !m_bLEDLiteState;
    }

#endif
}

/*************************************************
函数名： pushMonitorShutDown
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送系统自动休眠提示消息
*************************************************************/
void WebSocket::pushMonitorShutDown(void)
{
    QJsonObject data;
    QJsonObject monitorShutDown;
    PDS_SYS_INFO_LOG("pushMonitorShutDown");
    monitorShutDown.insert(STR_SHUT_DOWN, 0);
    data.insert(STR_MONITOR_SHUT_DOWN, monitorShutDown );
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(data)).toJson());
    }
}

/*************************************************
函数名： ~WebSocket()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
WebSocket::~WebSocket()
{
    if (m_pThread)
    {
        m_pThread->exit();
        m_pThread->wait();
        delete m_pThread;
    }
}

/*************************************************
函数名： instance()
输入参数： NULL
输出参数： NULL
返回值： 实例对象
功能： 获取实例
*************************************************************/
WebSocket* WebSocket::instance()
{
    static WebSocket *staticWebSocket = new WebSocket();
    return staticWebSocket;
}

/*************************************************
函数名： initInstance()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 初始化实例
*************************************************************/
void WebSocket::initInstance()
{
    instance();
}

/*************************************************
函数名： init()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 初始化
*************************************************************/
void WebSocket::init(quint16 port)
{
    if (m_webSocket->listen(QHostAddress::Any, port))
    {
        connect(m_webSocket, &QWebSocketServer::newConnection,
                this, &WebSocket::onNewConnection);
    }
    LogManager::instance()->logger(LOG1)->addReceiver(this, SLOT(onLogReceiver(QString,int)));
    m_iTimer = QObject::startTimer(WEBSOCKET_TIMER);
    m_iTimerPushMonitorTable = QObject::startTimer(MONITOR_TABLE_UPDATE_INTERVAL);
    m_pThread = new QThread;
    moveToThread(m_pThread);
    m_pThread->start();
    m_UpdateADUIDList.clear();
    strUpdateADUID.clear();
    strSyncDataADUID.clear();
    m_UpdateADUCount = 0;
    m_bLEDLiteState = true;

#ifdef SERVER_IS_OLD_PROTOCOL
    connect(&SmartSensor::instance(), SIGNAL(sigFirmUpdateProcess(QString,int)),
            this, SLOT(pushADUUpdateInfo(QString,int)));
    connect(&SmartSensor::instance(), SIGNAL(sigHisDataIndex(QString,bool)),
            this, SLOT(onOneRecordDone(QString,bool)));
    connect(&SmartSensor::instance(), SIGNAL(sigHisDataCount(QString,int)),
            this, SLOT(onADURecordCount(QString,int)));
    connect(&SmartSensor::instance(), SIGNAL(sigHisDataDone(QString,bool)),
            this, SLOT(onADUSyncState(QString,bool)));
#endif

#ifdef SERVER_LORA_BEAN
    connect(&MonitorService::instance(), SIGNAL(sigSyncHistoryRecordProgress(QVariant, Monitor::ProgressState)),
            this, SLOT(onADUDataSyncProgress(QVariant, Monitor::ProgressState)));
    //connect(&OutwardService::instance(), SIGNAL(sigReportHistoryRecordProgress(QVariant, Monitor::ProgressState)),\
            this, SLOT(onADUDataSyncProgress(QVariant, Monitor::ProgressState)));
    connect(&MonitorService::instance(), SIGNAL(sigFirmUpdateProgress(QVariant,Monitor::ProgressState,QString)),
            this, SLOT(pushADUUpdateInfo(QVariant,Monitor::ProgressState,QString)));
    connect(&MonitorService::instance(), SIGNAL(sigBusy()),
            this, SLOT(onLoraBeanBussy()));
    connect(&MonitorService::instance(), SIGNAL(sigIdle()),
            this, SLOT(onLoraBeanSpare()));
    //connect(&MonitorService::instance(), SIGNAL(sigFirmUpdateProgress(QVariant,Monitor::ProgressState)),\
            this, SLOT(pushADUUpdateInfo(QVariant,Monitor::ProgressState)));
    connect(&MonitorService::instance(), SIGNAL(sigConnectToADUResult(QString,bool)),
            this, SLOT(pushConnectToADUResult(QString,bool)));
    connect(&MonitorService::instance(), SIGNAL(sigSampleADU(QString,bool)),
            this, SLOT(onSampleADU(QString,bool)));

#endif
    //传感器日志获取进度状态
    connect(&MonitorService::instance(), SIGNAL(sigGetADULogProgress(QVariant, Monitor::ProgressState)), this, SLOT(onPushGetADULogProgress(QVariant, Monitor::ProgressState)));
    connect(&MonitorService::instance(), SIGNAL(sigGetADULogBegin()), this, SLOT(onGetAduLogBegin()));
    connect(&MonitorService::instance(), SIGNAL(sigGetADULogEnd()), this, SLOT(onGetAduLogEnd()));

    connect(&MonitorService::instance(), SIGNAL(sigADUOperateProgress(QVariant,Monitor::ProgressState,Monitor::MonitorOperateType)),\
                                        this, SLOT(onPushADUOperateProgress(QVariant,Monitor::ProgressState,Monitor::MonitorOperateType)));

    connect(&ConfigService::instance(), SIGNAL(sigADUConnectionChange()),
            this, SLOT(setMonitorTableHasUpdate()));

    qRegisterMetaType<ADUBusinessState>("ADUBusinessState");
    connect(&ConfigService::instance(), SIGNAL(sigADUConnectionChange(QString, ADUBusinessState, QString)),
            this, SLOT(onChnageAduState(QString, ADUBusinessState, QString)));
    connect(&MonitorService::instance(), SIGNAL(sigDelaySync(int)), this, SLOT(onDelaySync(int)));

    connect(this, SIGNAL(sigPushSampleResult(QString,bool)), this, SLOT(pushSampleResult(QString,bool)));

    connect(&MonitorService::instance(), SIGNAL(sigPushMonitorTaskStatus(QJsonObject)), this, SLOT(onPushMonitorTaskStatus(QJsonObject)));

    m_bMonitorTableUpdate = false;
}

/*************************************************
函数名： onLogReceiver
输入参数： strLogInfo -- 日志内容
        iLogLevel -- 日志等级
输出参数： NULL
返回值： NULL
功能： 响应日志输出信号
*************************************************************/
void WebSocket::onLogReceiver(QString strLogInfo, int iLogLevel)
{
    if (m_IsPushLogInfo && (iLogLevel >= m_PushLogInfoLevel))
    {
        for (int i = 0; i < m_clients.size(); i++)
        {
            QJsonObject data;
            data.insert(STR_PUSH_LOG_INFO, strLogInfo);
            m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(data)).toJson());
        }
    }
}

/*************************************************
函数名： onNewConnection()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应WebSocket连接上信号
*************************************************************/
void WebSocket::onNewConnection()
{
    QWebSocket *pSocket = m_webSocket->nextPendingConnection();

    connect(pSocket, &QWebSocket::textMessageReceived, this, &WebSocket::onTextMessageReceived);
    connect(pSocket, &QWebSocket::binaryMessageReceived, this, &WebSocket::onBinaryMessageReceived);
    connect(pSocket, &QWebSocket::disconnected, this, &WebSocket::socketDisconnected);
    m_clients << pSocket;

#ifdef WEBSOCKET_PRINT
    logInfo("WebSocket::onConnected, websocket connected")<<pSocket->peerAddress() << pSocket->peerPort();

#endif

}

/*************************************************
函数名： onError(QAbstractSocket::SocketError eCode)
输入参数： eCode：错误码
输出参数： NULL
返回值： NULL
功能： 响应WebSocket连接错误信号
*************************************************************/
void WebSocket::onError(QAbstractSocket::SocketError eCode)
{
    switch (eCode)
    {
    case QAbstractSocket::RemoteHostClosedError:
#ifdef WEBSOCKET_PRINT
        PDS_SYS_WARNING_LOG("WebSocket::onError, RemoteHostClosedError");
#endif
        break;
    case QAbstractSocket::ConnectionRefusedError:
#ifdef WEBSOCKET_PRINT
        PDS_SYS_WARNING_LOG("WebSocket::onError, ConnectionRefusedError");
#endif
        break;

    case QAbstractSocket::HostNotFoundError:
#ifdef WEBSOCKET_PRINT
        PDS_SYS_WARNING_LOG("WebSocket::onError, HostNotFoundError");
#endif
        break;
    default:
        break;
    }
}

/*************************************************
函数名： getADUUpdateInfo
输入参数： strADUUpdateFileName -- 更新文件名
        UpdateADUIDListData -- 更新前端列表
        uploadFile -- 更新文件内容
输出参数： NULL
返回值： NULL
功能： 获取前端更新的文件前端等信息
*************************************************************/
void WebSocket::getADUUpdateInfo(const QString &strADUUpdateFileName, const QStringList &UpdateADUIDListData, const QByteArray &uploadFile)
{
    m_UpdateADUIDList = UpdateADUIDListData;
    m_eADUUpdateFileName = strADUUpdateFileName;
    m_UpdateADUFile = uploadFile;
}

/*************************************************
函数名： isADUUpdateing
输入参数： NULL
输出参数： NULL
返回值： 前端更新是否可用
功能： 判断前端更新是否可用
*************************************************************/
bool WebSocket::isADUUpdateValid(void)
{
    bool bRet = false;
    if (m_UpdateADUIDList.isEmpty())
    {
        bRet = true;
    }
    return bRet;
}

/*************************************************
功能： 踢出账号在不同的页面重复登录
*************************************************************/
void WebSocket::kickOutSameClient(const QString& clientName, QWebSocket* client)
{
    for(int i = 0; i < m_clients.size(); i++)
    {
        if(m_clients[i]->objectName() == clientName && (client != m_clients[i]))
        {
            QWebSocket* pClient = m_clients[i];
            PDS_SYS_INFO_LOG("deleteRepeat %s", clientName.toLatin1().data());
            QJsonObject result;
            QJsonObject data;
            data.insert("repeatLogin",QString("%1").arg("repeatLogin"));
            result.insert(STR_REPEAT_LOGIN, data);
            pClient->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
            m_clients.removeAll(pClient);
            pClient->deleteLater();
        }
    }
}

/*************************************************
功能： 过滤出信号强度／信噪比／电量低的ADU具体数值信息并写到weakADUFilterfile.conf
*************************************************************/
void WebSocket::weakADU(void)
{
    QFile adustatefile("/home/<USER>/adustate.conf");
    if(!adustatefile.open(QIODevice::ReadOnly))
    {
        return;
    }
    QFile weakADUFilterfile("/home/<USER>/weakADUFilter.conf");
    //清空文件内容
    weakADUFilterfile.remove();
    if(!weakADUFilterfile.open(QIODevice::ReadWrite))
    {
        return;
    }

    QTextStream adustatefilestream(&adustatefile);
    QTextStream weakADUFilterfilestream(&weakADUFilterfile);

    //判断文件是否已经读到末尾
    while(!adustatefilestream.atEnd())
    {
        QString filterstr;
        bool filterflag = false;

        for(qint32 linenum = 1; linenum <= 15; linenum++)
        {
            QString tempstr;
            //读取一行数据
            tempstr = adustatefilestream.readLine();
            if(2 == linenum)
            {
                filterstr.append(tempstr);
                filterstr.append("\n");
            }
            else if(4 == linenum)
            {
                filterstr.append(tempstr);
                if(tempstr.toInt() < 1)
                {
                    filterflag = true;
                }
                filterstr.append("\n");
            }
            else if(6 == linenum)
            {
                filterstr.append(tempstr);
                if(tempstr.toInt() < 1)
                {
                    filterflag = true;
                }
                filterstr.append("\n");
            }
            else if(8 == linenum)
            {
                filterstr.append(tempstr);
                if(tempstr.toInt() < 50)
                {
                    filterflag = true;
                }
                filterstr.append("\n");
            }
        }
        //存在信号强度差／信噪比低／电量低的传感器,则把filterbuffer中的数据刷到weakADUFilterfile中
        if(true == filterflag)
        {
            weakADUFilterfilestream << filterstr << "\n";
        }
    }//end while
    adustatefile.close();
    weakADUFilterfile.close();
}

/*************************************************
功能： 备份数据导出
*************************************************************/
void WebSocket::exportData(const QJsonObject &jsonObject)
{
    QJsonObject exportData;
    if(jsonObject.value("ExportData").isObject())
    {
        exportData = jsonObject.value("ExportData").toObject();
        if(exportData.value("beginExportData").isArray()) // 开始导出
        {
            if(!m_spDataExport.isNull())
            {
                infoLog() << "is dataexporting";
                return;
            }
            QJsonArray beginExport = exportData.value("beginExportData").toArray();
            QList<QString> listPath;
            for(int i = 0; i < beginExport.size(); ++i)
            {
                QJsonValue value = beginExport.at(i);
                listPath.append(value.toObject().value("path").toString());
                infoLog() << "path" << listPath.last();
            }

            if(listPath.size())
            {
                m_spDataExport.reset(new DataExportManager);
                connect(this, SIGNAL(sigDataExport(const QList<QString> &)), m_spDataExport.data(), SLOT(onDataExport(const QList<QString> &)));
                connect(m_spDataExport.data(), SIGNAL(sigSendData(const QString &)), this, SLOT(onSendData(const QString &)));
                connect(m_spDataExport.data(), SIGNAL(sigCompressFinish()), this, SLOT(onCompressFinish()));
                emit sigDataExport(listPath);
                infoLog() << "sigDataExport";
            }
            else
            {
                warningLog() << "listPath error";
            }
        }
        else if(exportData.value("cancelExportData").isBool()) // 结束导出
        {
            if(!m_spDataExport.isNull())
            {
                m_spDataExport->cancelExport();
                m_spDataExport.clear();
            }
        }
    }
}

/*************************************************
功能： 刷新检测界面
*************************************************************/
void WebSocket::onPushMonitorTbInfo()
{
    for(int i = 0; i < m_clients.size(); i++)
    {
        QJsonObject data;
        data.insert("PushMonitorTbInfo", true);
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(data)).toJson());
    }
}

/*************************************************
函数名： onTextMessageReceived(const QString &strMessage)
输入参数： strMessage：字符串信息
输出参数： NULL
返回值： NULL
功能： 接收字符串信息
*************************************************************/
void WebSocket::onTextMessageReceived(const QString &strMessage)
{
    logTrace(strMessage);
    QJsonObject jsonMessage = QJsonDocument::fromJson(strMessage.toUtf8()).object();
    QWebSocket *pClient = qobject_cast<QWebSocket *>(sender());
    if(jsonMessage.value(STR_METHMOD_NAME).toString() == STR_WEBSOCKET_LOGIN)
    {
        QString strName = "";
        QString strWord = "";
        safe::UserManager::instance().rasDecrypt(jsonMessage.value(STR_USER_NAME).toString(), strName, true);
        safe::UserManager::instance().rasDecrypt(jsonMessage.value(STR_PASSWORD).toString(), strWord, true);

        safe::UserInfo userInfo;
        safe::OpResult result = safe::UserManager::instance().login(strName, strWord, "", userInfo);
        if( result == safe::OP_RESULT_SUCCESS )
        {
            // 同一账号在不同的页面重复登录
            //kickOutSameClient(strName, pClient);
            pClient->setObjectName(strName);
        }
        else  // 断开连接
        {
            PDS_SYS_INFO_LOG("close login");
            QJsonObject result;
            QJsonObject data;
            data.insert("closeLogin",QString("%1").arg("closeLogin"));
            result.insert("loginFail", data);
            pClient->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
            m_clients.removeAll(pClient);
            pClient->deleteLater();
        }
    }
    else if(jsonMessage.value(STR_METHMOD_NAME).toString() == STR_PUSH_UPDATE_ADU_PROGRESS)
    {
        if (jsonMessage.value(STR_ENABLE_PUSH).toBool())
        {
            m_UpdateADUCount = 0;
            strUpdateADUID.clear();
            //safe::AuditManager::instance().addAudit("user", safe::AUDIT_FIRM_SET, QDateTime::currentDateTime(), m_UpdateADUIDList.join('`'));
            startADUUpdate(m_eADUUpdateFileName, m_UpdateADUFile, m_UpdateADUIDList);
        }
    }
    else if (jsonMessage.value(STR_METHMOD_NAME).toString() == STR_PUSH_SYCN_ADU_DATA_PROGRESS)
    {
        if (jsonMessage.value(STR_ENABLE_PUSH).toBool())
        {
            QWebSocket *pClient = qobject_cast<QWebSocket *>(sender());
            if(pClient->objectName() == "")
            {
                QJsonObject result;
                pClient->sendTextMessage(QJsonDocument(encapsulationData(result, ERROR_COLDE_ILLEGAL_USER)).toJson());
                return;
            }

            safe::AuditManager::instance().addAudit(pClient->objectName(), safe::AUDIT_SYNC, QDateTime::currentDateTime());
            if(! safe::UserManager::instance().isPermission(pClient->objectName(),safe::OPERATE_DATA_SYNC) )
            {
                QJsonObject result;
                pClient->sendTextMessage(QJsonDocument(encapsulationData(result, ERROR_COLDE_NO_PERMISSION)).toJson());
                return;
            }

            //safe::AuditManager::instance().addAudit("user", safe::AUDIT_SYNC, QDateTime::currentDateTime(), "");
            QJsonObject parameters = jsonMessage.value("parameters").toObject();
            m_SyncADUDataADUList = parameters.value("aduList").toString().split(",");
            QDateTime startTime = QDateTime(QDate::fromString(parameters.value(STR_BEGIN_DATE).toString(), STR_DATE_QSTRING_CNA));
            QDateTime endTime = QDateTime(QDate::fromString(parameters.value(STR_END_DATE).toString(), STR_DATE_QSTRING_CNA).addDays(1));
            m_ADUType =  ConfigService::instance().getADUTypEnum(parameters.value(STR_ADU_TYPE).toString());
            m_SyncADUDataADUCount = 0;
            m_SyncADUDataRecordCount = 0;
            m_SyncADUDataRecordCurrentCount = 0;
            m_SyncADUDataFalseRecordCount = 0;
            m_SyncADUDataSuccedRecordCount = 0;
            strSyncDataADUID.clear();
            m_listSyncFinish.clear();

            if (ADU_IS == m_ADUType)
            {
                SmartSensor::instance().syncHistoryData(m_SyncADUDataADUList, startTime, endTime);
            }
            else if((ADU_HFCT_IS == m_ADUType)
                    || (ADU_MECH_IS == m_ADUType)
                    || (ADU_UHF_IS == m_ADUType)
                    || (ADU_PD_IS == m_ADUType)
                    || (ADU_ARRESTER_I_IS == m_ADUType)
                    || (ADU_ARRESTER_U_IS == m_ADUType)
                    || (ADU_GROUNDDINGCURRENT_IS == m_ADUType)
                    || (ADU_LEAKAGECURRENT_IS == m_ADUType)
                    || (ADU_VIBRATION_IS == m_ADUType)
                    || (ADU_TRANSFORMER_AE_IS == m_ADUType)
                    || (ADU_GIS_AE_IS == m_ADUType)
                    || (ADU_TEMP_HUM_IS == m_ADUType)
                    || (ADU_FLOOD_IS == m_ADUType)
                    || (ADU_TEVPRPS_IS == m_ADUType))
            {
                MonitorService::instance().syncData(m_SyncADUDataADUList, m_ADUType,startTime, endTime);
                //OutwardService::instance().reportHistoryData(m_SyncADUDataADUList, m_ADUType,startTime, endTime);
            }
            else if (ADU_MECH == m_ADUType)
            {
                QList<quint8> ucADUList;
                for (int i = 0; i < m_SyncADUDataADUList.size(); i++)
                {
                    ucADUList.append(m_SyncADUDataADUList.at(i).toInt());
                }
                MechMonitor::instance().syncHistoryMechRecord(ucADUList, startTime, endTime);
            }
            else
            {

            }            
        }
        else
        {
#ifdef SERVER_IS_OLD_PROTOCOL
            SmartSensor::instance().stopSyncHistoryData();
#endif

#ifdef SERVER_MECH
            MechMonitor::instance().stopSyncHistoryMechRecord();
#endif

#ifdef SERVER_LORA_BEAN
            MonitorService::instance().stopSyncData(m_ADUType);
#endif
            QThread::sleep(2);
            pushSyncADUDataFinsh(true);
        }
    }
    else if(jsonMessage.value(STR_METHMOD_NAME).toString() == "operateADU")
    {
        QJsonObject parameters = jsonMessage.value("parameters").toObject();
        QString strADUId = parameters.value(STR_ADU_ID).toString();
        ADUUnitInfo aduInfo;
        ADUType eADUType;
        if(ConfigService::instance().findADU(strADUId, aduInfo))
        {
            eADUType = aduInfo.eType;
        }else
        {
            return;
        }
        int value = parameters.value("value").toInt();
        OperateInfoBaseType opType;
        if(value == 0)
        {
            opType = OperateInfoBaseType_OPEN;
        }
        else
        {
            opType = OperateInfoBaseType_CLOSE;
        }
        MonitorService::instance().operateADU(strADUId, eADUType, opType);
    }
    /*else if(jsonMessage.value(STR_METHMOD_NAME).toString() == "WeakADUFilter")
    {
        weakADU();
    }*/
    else if (jsonMessage.value(STR_METHMOD_NAME).toString() == STR_PUSH_SAMPLE_PROGRESS)
    {

        QWebSocket *pClient = qobject_cast<QWebSocket *>(sender());
        if(pClient->objectName() == "")
        {
            QJsonObject result;
            pClient->sendTextMessage(QJsonDocument(encapsulationData(result, ERROR_COLDE_ILLEGAL_USER)).toJson());
            return;
        }

        if(! safe::UserManager::instance().isPermission(pClient->objectName(),safe::OPERATE_DATA_COLLECT))
        {
            QJsonObject result;
            pClient->sendTextMessage(QJsonDocument(encapsulationData(result, ERROR_COLDE_NO_PERMISSION)).toJson());
            return;
        }

        if (jsonMessage.value(STR_ENABLE_PUSH).toBool())
        {
            QJsonObject parameters = jsonMessage.value("parameters").toObject();
            QString strADUId = parameters.value(STR_ADU_ID).toString();
            QString strPointId = parameters.value(STR_POINT_ID).toString();
            ADUType eADUType =  ConfigService::instance().getADUTypEnum(parameters.value(STR_ADU_TYPE).toString());
            auto adutypes = ConfigService::instance().getAduTypeListFromPoint(strPointId);
            auto aduids = ConfigService::instance().getAduIdListFromPoint(strPointId);
            ADUOperationType eADUOperationType = (ADUOperationType)parameters.value(STR_ADU_OPERATION_TYPE).toInt();

            if(APPLY_TO_ONE_POINT == eADUOperationType || APPLY_TO_ONE_ADU == eADUOperationType)
            {
                safe::AuditManager::instance().addAudit(pClient->objectName(), safe::AUDIT_SINGLE_SAMPLE, QDateTime::currentDateTime());
            }
            else
            {
                safe::AuditManager::instance().addAudit(pClient->objectName(), safe::AUDIT_BATCH_SAMPLE, QDateTime::currentDateTime());
            }

            if (eADUOperationType != APPLY_TO_ALL_ADU && strADUId.isEmpty())
            {
                if (adutypes.empty() || aduids.empty())
                {
                    PDS_SYS_ERR_LOG("cannot get adu with pointid: %s", strPointId.toLatin1().data());
                    return;
                }
                eADUType = adutypes.front();
                strADUId = aduids.front();
            }
            QStringList listADUID;
            std::function<void(const QString&, bool)> fSampleStatusReport =
                    [this](const QString& aduId, bool result)
            {

                emit sigPushSampleResult(aduId, result);

            };
            //QList<QString> switchADU = ConfigService::instance().getSwitchADU();
            switch (eADUOperationType) {
            case APPLY_TO_ALL_ADU:
            {
                PDS_SYS_INFO_LOG("start sample all testPoint data");
                QStringList pointConnectAduList = ConfigService::instance().getTestPointConnectAdus();

                QMap<ADUType, QStringList> qmap4AduTypeList;
                foreach (const QString &strAduID, pointConnectAduList) {
                    ADUType eCurrentAduType;
                    ConfigService::instance().getADUTypeFromID(strAduID, eCurrentAduType);
                    qmap4AduTypeList[eCurrentAduType].append(strAduID);
                }

                for(auto iter = qmap4AduTypeList.cbegin(); iter != qmap4AduTypeList.cend(); ++iter)
                {
                    MonitorService::instance().sampleData(iter.value(), iter.key());
                }
            }
#if 0
                PDS_SYS_INFO_LOG("start sample all adu data");
                //safe::AuditManager::instance().addAudit("user", safe::AUDIT_BATCH_SAMPLE, QDateTime::currentDateTime(), "");
                MonitorService::instance().sampleData(listADUID, ADU_TEMPFC_OUT, fSampleStatusReport);
                for(auto i = 0; i < switchADU.size(); i++)
                {
                    ADUType aduType =  ConfigService::instance().getADUTypEnum(switchADU[i]);
                    if(ConfigService::instance().getISADUType(aduType))
                    {
                        MonitorService::instance().sampleData(listADUID, aduType, fSampleStatusReport);
                    }
                    else
                    {
                        MonitorService::instance().sampleData(listADUID, aduType);
                    }
                }
#endif
                break;
            case APPLY_TO_SAME_TYPE_ADU:
                //safe::AuditManager::instance().addAudit("user", safe::AUDIT_BATCH_SAMPLE, QDateTime::currentDateTime(), "");
                if ( ConfigService::instance().getISADUType( eADUType ) )
                {
                    MonitorService::instance().sampleData(listADUID, eADUType, fSampleStatusReport);
                }
                else if(MoistureController::instance().isMoistureAdu(eADUType))
                {
                    QStringList adus = ConfigService::instance().ADUID(eADUType);
                    for(int i = 0; i < adus.size(); ++i)
                    {
                        MoistureController::instance().singleSample(adus[i]);
                    }
                }
                break;
            case APPLY_TO_ONE_ADU:
                //safe::AuditManager::instance().addAudit("user", safe::AUDIT_SINGLE_SAMPLE, QDateTime::currentDateTime(), "");
                if ( ConfigService::instance().getISADUType( eADUType ) )
                {
                    listADUID.append(strADUId);
                    MonitorService::instance().sampleData(listADUID, eADUType, fSampleStatusReport);
                }
                else if(MoistureController::instance().isMoistureAdu(eADUType)
                        && ConfigService::instance().isADUIDExisted(strADUId))
                {
                    MoistureController::instance().singleSample(strADUId);
                }
                break;
            case APPLY_TO_ONE_POINT:    //单次采集
            {
                //safe::AuditManager::instance().addAudit("user", safe::AUDIT_SINGLE_SAMPLE, QDateTime::currentDateTime(), "");
                TestPointInfo stPoint;
                ConfigService::instance().getTestPoint(strPointId, stPoint);
                for (int i = 0; i < stPoint.ConnectionInfo.size(); i++)
                {
                    ConfigService::instance().getADUTypeFromID(stPoint.ConnectionInfo.at(i).strID, eADUType);

                    if(MoistureController::instance().isMoistureAdu(eADUType))
                    {
                        MoistureController::instance().singleSample(stPoint.ConnectionInfo.at(i).strID);
                    }
                    else
                    {
                        listADUID.append(stPoint.ConnectionInfo.at(i).strID);
                    }
                }
                MonitorService::instance().sampleData(listADUID, eADUType, fSampleStatusReport);

            }
                break;
            default:

                break;
            }

        }
        else
        {
        }
    }
    else if (jsonMessage.value(STR_METHMOD_NAME).toString() == STR_PUSH_GET_ADU_LOG_PROGRESS)
    {
        if (jsonMessage.value(STR_ENABLE_PUSH).toBool())
        {
            QWebSocket *pClient = qobject_cast<QWebSocket *>(sender());
            if(pClient->objectName() == "")
            {
                QJsonObject result;
                pClient->sendTextMessage(QJsonDocument(encapsulationData(result, ERROR_COLDE_ILLEGAL_USER)).toJson());
                return;
            }

            QJsonObject parameters = jsonMessage.value("parameters").toObject();           
            QStringList getLogADUList = parameters.value("aduList").toString().split(",");
            QDateTime startTime = QDateTime(QDate::fromString(parameters.value(STR_BEGIN_DATE).toString(), STR_DATE_QSTRING_CNA));
            QDateTime endTime = QDateTime(QDate::fromString(parameters.value(STR_END_DATE).toString(), STR_DATE_QSTRING_CNA).addDays(1));
            m_ADUType =  ConfigService::instance().getADUTypEnum(parameters.value(STR_ADU_TYPE).toString());

            if(ConfigService::instance().getISADUType(m_ADUType))
            {
                MonitorService::instance().getADULog(getLogADUList, m_ADUType, true, startTime, endTime);
            }
        }
        else
        {
            //取消传感器日志导出操作
            QJsonObject parameters = jsonMessage.value("parameters").toObject();
            MonitorService::instance().stopGetADULog(ConfigService::instance().getADUTypEnum(parameters.value(STR_ADU_TYPE).toString()));
        }
    }
	else if (jsonMessage.value(STR_METHMOD_NAME).toString() == STR_PUSH_GET_ADU_ALARM_PROGRESS)
    {
        if (jsonMessage.value(STR_ENABLE_PUSH).toBool())
        {
            QWebSocket *pClient = qobject_cast<QWebSocket *>(sender());
            if(pClient->objectName() == "")
            {
                QJsonObject result;
                pClient->sendTextMessage(QJsonDocument(encapsulationData(result, ERROR_COLDE_ILLEGAL_USER)).toJson());
                return;
            }

            QJsonObject parameters = jsonMessage.value("parameters").toObject();
            QStringList getAlarmADUList = parameters.value("aduList").toString().split(",");
            m_CurrentOperateAduList = getAlarmADUList;
            bool bGetAllData = parameters.value("enableGetAllData").toBool();   //是否获取所有数据
            QDateTime startTime = QDateTime(QDate::fromString(parameters.value(STR_BEGIN_DATE).toString(), STR_DATE_QSTRING_CNA));
            QDateTime endTime = QDateTime(QDate::fromString(parameters.value(STR_END_DATE).toString(), STR_DATE_QSTRING_CNA).addDays(1));
            m_ADUType =  ConfigService::instance().getADUTypEnum(parameters.value(STR_ADU_TYPE).toString());

            if(ConfigService::instance().getISADUType(m_ADUType))
            {
                MonitorService::instance().getADUAlarmData(getAlarmADUList, m_ADUType, bGetAllData, startTime, endTime);
            }
        }
        else
        {
            //取消传感器告警数据获取操作
            QJsonObject parameters = jsonMessage.value("parameters").toObject();
            MonitorService::instance().stopGetADUAlarmData(ConfigService::instance().getADUTypEnum(parameters.value(STR_ADU_TYPE).toString()));

            m_CurrentOperateAduList.clear();
            m_CurrentOperateFinishAduList.clear();
        }
    }
    else
    {
        infoLog() << "strMessage" << strMessage;
        if(jsonMessage.value("result").isObject())
        {
            QJsonObject resultObject =  jsonMessage.value("result").toObject();
            exportData(resultObject);
        }

    }
}


/*************************************************
输入参数：delayMine -- 延时时间(分钟)
功能： 相应前端数据同步结果
*************************************************************/
void WebSocket::onDelaySync(int delayMine)
{
    QJsonObject result;
    QJsonObject data;
    data.insert("delayMine",QString("%1").arg(delayMine));
    result.insert(STR_PUSH_DELAY_SYNC, data);

    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }
}

void WebSocket::onSendData(const QString &message)
{
    infoLog() << "onSendData" << message;
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(message);
    }
}

void WebSocket::onCompressFinish()
{
    infoLog() << "onCompressFinish";
    if(!m_spDataExport.isNull())
    {
        m_spDataExport.clear();
    }
}

void WebSocket::setSaveAduResult(const QString &strADUId, bool saveResult)
{
    QJsonObject result;
    QJsonObject data;
    data.insert("strAduID", strADUId);
    if(saveResult)
    {
        data.insert("msg", "成功");
    }
    else
    {
        data.insert("msg", "失败");
    }
    data.insert("success", saveResult);

    result.insert("PushAudSaveResult",data);
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }
}

void WebSocket::onLogout(const QString strUserName)
{
    logInfo("--TEST--WebSocket::logout, current client size:") << m_clients.size();
    for (int i = 0; i < m_clients.size(); ++i)
    {
        if(m_clients.at(i)->objectName() == strUserName)
        {
            logInfo("client logout:") << m_clients.at(i)->peerAddress().toString() << m_clients.at(i)->peerPort();
            m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(ERROR_CODE_LOG_OUT)).toJson());
        }
    }
}


/*************************************************
函数名： onBinaryMessageReceived(const QByteArray &baData)
输入参数： baData：二进制信息
输出参数： NULL
返回值： NULL
功能： 接收二进制信息
*************************************************************/
void WebSocket::onBinaryMessageReceived(const QByteArray &baData)
{
    QWebSocket *pClient = qobject_cast<QWebSocket *>(sender());
    if (pClient)
    {
        pClient->sendBinaryMessage(baData);
    }
}

/*************************************************
函数名： socketDisconnected
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应client断开连接
*************************************************************/
void WebSocket::socketDisconnected()
{
    QWebSocket *pClient = qobject_cast<QWebSocket *>(sender());
    PDS_SYS_INFO_LOG("websocket closed, ip %s, port %d", pClient->peerAddress().toString().toLatin1().data(), pClient->peerPort());
    if (pClient)
    {
        m_clients.removeAll(pClient);
        pClient->deleteLater();
    }
}

/*************************************************
函数名： stopADUUpdate
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 中断前端固件更新
*************************************************************/
void WebSocket::stopUpdateADU(void)
{
    stopADUUpdate(m_eADUUpdateFileName);
    QJsonObject result;
    QJsonObject data;
    data.insert("isDone","cancel");
    result.insert(STR_UPDATE_ADU_PROGRESS,data);
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }
    m_UpdateADUIDList.clear();
}

/*************************************************
函数名： pushADUUpdateInfo
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送固件更新进度
*************************************************************/
void WebSocket::pushADUUpdateInfo(const QString &strAduID, int step, QString strErrorMsg)
{
    Q_UNUSED(strErrorMsg)

    if(m_UpdateADUIDList.isEmpty())
    {
        return;
    }
    if ((m_UpdateADUIDList.contains(strAduID)) || (strAduID.toUpper() == "FF:FF:FF:FF:FF:FF:FF:FF"))
    {
//        if ((strUpdateADUID != strAduID) && (strAduID.toUpper() != "FF:FF:FF:FF:FF:FF:FF:FF"))
//        {
//            strUpdateADUID = strAduID;
//            // m_UpdateADUCount++;
//        }

        int percentValue = step;
        //固件更新失败
        if ((step == -1) || (step == 101))
        {
            QJsonObject result;
            QJsonObject data;
            data.insert("aduId",strAduID);
            result.insert(STR_UPDATE_ADU_PROGRESS,data);
            for (int i = 0; i < m_clients.size(); i++)
            {
                m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
            }

            if(!m_listAduUpdateFinish.contains(strAduID) && (strAduID.toUpper() != "FF:FF:FF:FF:FF:FF:FF:FF"))
            {
                m_listAduUpdateFinish.append(strAduID);
            }
            percentValue = 0;
        }

        //进度信息推送
        QJsonObject result;
        QJsonObject data;
        const int progressIndex = m_listAduUpdateFinish.size() < m_UpdateADUIDList.size() ? m_listAduUpdateFinish.size() + 1 : m_UpdateADUIDList.size();
        data.insert("progress",QString("%1/%2").arg(progressIndex).arg(m_UpdateADUIDList.size()));
        data.insert("percent",QString("%1").arg(percentValue));
        result.insert(STR_UPDATE_ADU_PROGRESS,data);
        for (int i = 0; i < m_clients.size(); i++)
        {
            m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
        }


        if(step == 100)
        {
            if(!m_listAduUpdateFinish.contains(strAduID) && (strAduID.toUpper() != "FF:FF:FF:FF:FF:FF:FF:FF"))
            {
                m_listAduUpdateFinish.append(strAduID);
            }
        }

        //固件更新结束
        if (m_listAduUpdateFinish.size() >= m_UpdateADUIDList.size() && ((step == 100) || (step == -1) || (step == 101)))
        {
            QJsonObject result;
            QJsonObject data;

            data.insert("isDone","update");

            result.insert(STR_UPDATE_ADU_PROGRESS,data);
            for (int i = 0; i < m_clients.size(); i++)
            {
                m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
            }
            m_UpdateADUIDList.clear();
            m_listAduUpdateFinish.clear();
            m_UpdateADUFile.clear();
            m_eADUUpdateFileName.clear();
            strUpdateADUID.clear();
            m_UpdateADUCount = 0;
        }
    }
}

/*************************************************
函数名： pushADUUpdateInfo
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送固件更新进度
*************************************************************/
void WebSocket::pushADUUpdateInfo(quint8 ucAduID, MechDefine::UpdateProgressState eUpdateProgressState)
{
    pushADUUpdateInfo(QString::number(ucAduID),int(eUpdateProgressState));
}

/*************************************************
函数名： pushADUUpdateInfo
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送固件更新进度
*************************************************************/
void WebSocket::pushADUUpdateInfo(QVariant AduID, Monitor::ProgressState eUpdateProgressState, QString strErrorMsg)
{
    pushADUUpdateInfo(AduID.toString(),int(eUpdateProgressState), strErrorMsg);
}

/*************************************************
函数名： onOneRecordDone
输入参数： strmac -- mac地址
            bFlag -- 单挑数同步结果
输出参数： NULL
返回值： NULL
功能： 相应单条数据同步结果
*************************************************************/
void WebSocket::onOneRecordDone(const QString &strmac, bool bFlag)
{
    Q_UNUSED(strmac);
    if (bFlag)
    {
        m_SyncADUDataSuccedRecordCount++;
    }
    else
    {
        m_SyncADUDataFalseRecordCount++;
    }
    m_SyncADUDataRecordCurrentCount++;
    pushSyncADUDataInfo();
}

/*************************************************
函数名： onADURecordNum
输入参数： strmac -- mac地址
            iCount -- 当前前端历史数据条数
输出参数： NULL
返回值： NULL
功能： 相应某前端历史数据条数
*************************************************************/
void WebSocket::onADURecordCount(const QString &strmac, int iCount)
{
    Q_UNUSED(strmac);
    m_SyncADUDataRecordCount = iCount;
    pushSyncADUDataInfo();
}

/*************************************************
函数名： onADUSyncState
输入参数： strmac -- mac地址
            bFlag -- 单挑数同步结果
输出参数： NULL
返回值： NULL
功能： 相应某前端数据同步结果
*************************************************************/
void WebSocket::onADUSyncState(const QString &strmac, bool bCount)
{
    Q_UNUSED(strmac);
    Q_UNUSED(bCount);
    m_SyncADUDataADUCount++;
    m_SyncADUDataRecordCurrentCount = 0;
    pushSyncADUDataInfo();
}

/*************************************************
函数名： pushADUUpdateInfo
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送前端数据同步进度
*************************************************************/
void WebSocket::pushSyncADUDataInfo(void)
{
    if (!m_SyncADUDataADUList.isEmpty())
    {
        QJsonObject result;
        QJsonObject data;
        data.insert("progress",QString("%1/%2").arg(m_SyncADUDataADUCount).arg(m_SyncADUDataADUList.size()));
        data.insert("percent",QString("%1").arg(m_SyncADUDataRecordCurrentCount));
        result.insert(STR_SYCN_ADU_DATA_PROGRESS,data);

        for (int i = 0; i < m_clients.size(); i++)
        {
            m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
        }
        if (m_SyncADUDataADUCount > m_SyncADUDataADUList.size())
        {
            pushSyncADUDataFinsh();
        }
    }
}

/*************************************************
函数名： pushADUUpdateInfo
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送前端数据同步进度
*************************************************************/
void WebSocket::pushSyncADUDataFinsh(bool bIscancle)
{
    QJsonObject result;

    QJsonObject isDone;

    if (bIscancle)
    {
        isDone.insert("cancel","done");
    }
    else
    {
        isDone.insert("isDone","done");
    }
    result.insert(STR_SYCN_ADU_DATA_PROGRESS,isDone);
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }

    m_SyncADUDataADUList.clear();
    m_listSyncFinish.clear();
    strSyncDataADUID.clear();
    m_SyncADUDataADUCount = 0;
    m_SyncADUDataRecordCount = 0;
    m_SyncADUDataRecordCurrentCount = 0;
    m_SyncADUDataFalseRecordCount = 0;
    m_SyncADUDataSuccedRecordCount = 0;
}

/*************************************************
函数名： pushADUUpdateInfo
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送连接前端结果
*************************************************************/
void WebSocket::pushConnectToADUResult(const QString &strAduID, bool bRet)
{
    QJsonObject result;
    QJsonObject isDone;

    isDone.insert(STR_ADU_ID,strAduID);
    isDone.insert(STR_RESULT,bRet);

    if (bRet)
    {
        m_iConnectionADUTiemout = QObject::startTimer(10 * 60 * 1000); // 10分钟
        m_strConnectedADUID = strAduID;
    }
    result.insert(STR_PUHS_CONNECTED_ADU_RESULT,isDone);
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }
}

/*************************************************
函数名： onSampleADU
输入参数： strAduID  前端id
         bRet  操作结果
输出参数： NULL
返回值： NULL
功能： 响应前端采集
*************************************************************/
void WebSocket::onSampleADU(const QString &strAduID, bool bRet)
{
    Q_UNUSED(strAduID);
    if (bRet)
    {
        killTimer(m_iConnectionADUTiemout);
        m_iConnectionADUTiemout = QObject::startTimer(10 * 60 * 1000); // 10分钟
    }
}

/*************************************************
输入参数： strAduID  前端id
功能： 传感器版本低提示
*************************************************************/
void WebSocket::onAduVersionLowTip(const QString &strAduID)
{
    QJsonObject result;
    result.insert("aduVersionLowTip", strAduID);
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }
}

/*************************************************
输入参数： ADUId  前端id
         message  信息
功能： 相应日志获取
*************************************************************/
void WebSocket::onPushGetADULogProgress(QVariant ADUId, Monitor::ProgressState eState)
{
    /*QJsonObject result;
    QJsonObject logProgress;
    QJsonValue stateValue(eState == Monitor::PROGRESS_STATE_FAIL? "fail" : "");

    logProgress.insert("ADUId", QJsonValue(ADUId.toString()));
    logProgress.insert("progressState", stateValue);

    result.insert(STR_PUSH_ADU_LOG, QJsonValue(logProgress));
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }
    logInfo("###@@@@onPushGetADULogProgress") <<  QString(QJsonDocument(encapsulationData(result)).toJson());
    */

    QMap<QString, QString> map4Progress;
    map4Progress.insert(QStringLiteral("ADUId"), ADUId.toString());
    map4Progress.insert(QStringLiteral("progressState"), eState == Monitor::PROGRESS_STATE_FAIL? QStringLiteral("fail") : "");
    pushGetAduLogProgress(map4Progress);
}

void WebSocket::onGetAduLogBegin()
{  
    QMap<QString, QString> map4Progress;
    map4Progress.insert(QStringLiteral("getADULogFile"), QStringLiteral("begin"));
    pushGetAduLogProgress(map4Progress);
}

void WebSocket::onGetAduLogEnd()
{
    //获取传感器日志文件结束
    QMap<QString, QString> map4Progress;
    map4Progress.insert(QStringLiteral("getADULogFile"), QStringLiteral("end"));
    pushGetAduLogProgress(map4Progress);

    //检查传感器日志存放路径是否存在文件
    QDir aduLogDir(ADU_LOG_SAVE_PATH, "*.log", QDir::NoSort, QDir::Files);
    if(aduLogDir.count() > 0)
    {
        //移除旧文件
        bool bRet = QFile::remove(ADU_LOG_SAVE_PATH + "/ADULogFile.tar.gz");

        logInfo("###@@@@onGetAduLogEnd") <<  aduLogDir.count() << bRet;

        //压缩传感器日志文件开始
        map4Progress.clear();
        map4Progress.insert(QStringLiteral("LogFileCompress"), QStringLiteral("begin"));
        pushGetAduLogProgress(map4Progress);

        //开始压缩
        QStringList tarArguments("-czf");
        tarArguments << "ADULogFile.tar.gz" << "." ;
        QProcess tarCommand;
        tarCommand.setWorkingDirectory(ADU_LOG_SAVE_PATH);
        tarCommand.start("tar", tarArguments);
        tarCommand.waitForFinished(5*60*1000);

        //压缩结束       
        map4Progress.clear();
        map4Progress.insert(QStringLiteral("LogFileCompress"), QStringLiteral("end"));
        map4Progress.insert(QStringLiteral("LogFilePath"), ADU_LOG_SAVE_PATH + "/ADULogFile.tar.gz");
        pushGetAduLogProgress(map4Progress);

        //清除所有log文件
        system(QString("rm -f %1").arg(ADU_LOG_SAVE_PATH + "/*.log").toStdString().c_str());
    }
    else
    {
        map4Progress.clear();
        map4Progress.insert(QStringLiteral("CheckLogFilePath"), QStringLiteral("empty"));
        pushGetAduLogProgress(map4Progress);
    }

}

void WebSocket::pushGetAduLogProgress(const QMap<QString, QString> &map4Progress)
{
    QJsonObject result;
    QJsonObject logProgress;

    for(auto iter = map4Progress.constBegin(); iter != map4Progress.constEnd(); ++iter)
    {
        logProgress.insert(iter.key(), QJsonValue(iter.value()));
    }
    result.insert(STR_PUSH_ADU_LOG, QJsonValue(logProgress));

    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }
    logInfo("###@@@@pushGetAduLogProgress") <<  QString(QJsonDocument(encapsulationData(result)).toJson());
}

/*************************************************
函数名： onDisconnectionADUTimeOut
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应前端唤醒超时（将断开前端的链接）
*************************************************************/
void WebSocket::onDisconnectionADUTimeOut()
{
    if (!m_strConnectedADUID.isEmpty())
    {
        ADUType eADUType;
        ConfigService::instance().getADUTypeFromID(m_strConnectedADUID, eADUType);

        MonitorService::instance().disconnectionADU(m_strConnectedADUID, eADUType);
        QJsonObject result;
        QJsonObject isDone;

        isDone.insert(STR_ADU_ID, m_strConnectedADUID);
        isDone.insert(STR_RESULT, true);

        result.insert(STR_PUHS_DISCONNECTED_ADU_RESULT,isDone);
        for (int i = 0; i < m_clients.size(); i++)
        {
            m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
        }
        m_strConnectedADUID.clear();
    }
}

/*************************************************
函数名： setMonitorTableHasUpdate
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 设置监测表有更新
*************************************************************/
void WebSocket::setMonitorTableHasUpdate()
{
    m_bMonitorTableUpdate = true;
}


/*************************************************
输入参数： strAduID 前端ID
         state 前端状态
输出参数： NULL
返回值： NULL
功能： 更改前端状态
*************************************************************/
void WebSocket::onChnageAduState(QString strAduID, ADUBusinessState state, QString time)
{
    QJsonObject data;
    data.insert("strAduID", strAduID);
    data.insert("state", "");
    data.insert("time", time);
    QJsonObject result;
    result.insert("UpdateADUState", data);

    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }
}

void WebSocket::onPushADUOperateProgress(QVariant aduID, Monitor::ProgressState eState, Monitor::MonitorOperateType eOperateType)
{
    int step = (int)eState;
    QString strAduID = aduID.toString();
    if(m_CurrentOperateAduList.isEmpty())
    {
        return;
    }

    if ((m_CurrentOperateAduList.contains(strAduID)) || (strAduID.toUpper() == "FF:FF:FF:FF:FF:FF:FF:FF"))
    {
        QString strOperateType;  //当前操作类型
        switch (eOperateType)
        {
        case Monitor::MonitorOperateType::Operate_TYPE_GETADUALARMDATA:
        {
            strOperateType = QStringLiteral(STR_PUSH_GET_ADU_ALARM_PROGRESS);
        }
            break;
        default:
            break;
        }

        //操作失败
        int percentValue = step;
        if ((step == -1) || (step == 101))
        {
            QJsonObject result;
            QJsonObject data;
            data.insert("aduId", strAduID);
            result.insert(strOperateType, data);

            for (int i = 0; i < m_clients.size(); i++)
            {
                m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
            }

            if(!m_CurrentOperateFinishAduList.contains(strAduID) && (strAduID.toUpper() != "FF:FF:FF:FF:FF:FF:FF:FF"))
            {
                m_CurrentOperateFinishAduList.append(strAduID);
            }
            percentValue = 0;
        }

        //操作进度推送
        QJsonObject result;
        QJsonObject data;
        const int progressIndex = m_CurrentOperateFinishAduList.size() < m_CurrentOperateAduList.size() ? m_CurrentOperateFinishAduList.size() + 1 : m_CurrentOperateAduList.size();
        data.insert("progress",QString("%1/%2").arg(progressIndex).arg(m_CurrentOperateAduList.size()));
        data.insert("percent",QString("%1").arg(percentValue));
        result.insert(strOperateType, data);
        for (int i = 0; i < m_clients.size(); i++)
        {
            m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
        }

        if(step == 100)
        {
            if(!m_CurrentOperateFinishAduList.contains(strAduID) && (strAduID.toUpper() != "FF:FF:FF:FF:FF:FF:FF:FF"))
            {
                m_CurrentOperateFinishAduList.append(strAduID);
            }
        }


        //操作结束消息推送
        if (m_CurrentOperateFinishAduList.size() >= m_CurrentOperateAduList.size() && ((step == 100) || (step == -1) || (step == 101)))
        {
            QJsonObject result;
            QJsonObject data;

            data.insert("isDone", "update");

            result.insert(strOperateType, data);

            for (int i = 0; i < m_clients.size(); i++)
            {
                m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
            }

           m_CurrentOperateAduList.clear();
           m_CurrentOperateFinishAduList.clear();
        }
    }
}

void WebSocket::onPushMonitorTaskStatus(QJsonObject taskStatus)
{
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(taskStatus).toJson());
        logInfo("onPushMonitorTaskStatus:") << QString(QJsonDocument(taskStatus).toJson());
    }
}

/*************************************************
函数名： pushADUConnectionInfo
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送前端连接状态信息
*************************************************************/
void WebSocket::pushADUConnectionInfo(void)
{
    QJsonObject data;
    data.insert(STR_PUSH_MINITOR_TB_INFO, true);
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(data)).toJson());
    }
}

/*************************************************
函数名： pushSampleProgress
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送单次采集进度
*************************************************************/
void WebSocket::pushSampleProgress(const QString &strProgressInfo, int iProgressPercent)
{
    QJsonObject data;
    data.insert("percent", iProgressPercent);
    data.insert("progressInfo", strProgressInfo);
    if(iProgressPercent == 100)
    {
        emit sigWorkState(false);
    }
    QJsonObject result;
    result.insert(STR_PUSH_SAMPLE_PROGRESS, data);
    for (int i = 0; i < m_clients.size(); i++)
    {
        m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
    }
}

/*************************************************
函数名： pushSampleProgress
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 推送某个前端的采集结果
*************************************************************/
void WebSocket::pushSampleResult(const QString &aduId, bool sampleStatus)
{
    QJsonObject data;
    QJsonObject result;
    data.insert("aduId", aduId);
    data.insert("sampleResult", sampleStatus);
    result.insert(STR_PUSH_SAMPLE_STATUS, data);
}

/*************************************************
函数名： onADUDataSyncProgress
输入参数： ADUID -- mac地址
            eProgressState -- 进度
输出参数： NULL
返回值： NULL
功能： 相应前端数据同步结果
*************************************************************/
void WebSocket::onADUDataSyncProgress(QVariant ADUID, Monitor::ProgressState eProgressState)
{
    onADUDataSyncProgress(ADUID.toString(), (int)eProgressState);
}
void WebSocket::onADUDataSyncProgress(quint8 ucADUID, MechDefine::UpdateProgressState eProgressState)
{
    onADUDataSyncProgress(QString::number(ucADUID),(int)eProgressState);
}
void WebSocket::onADUDataSyncProgress(const QString &strADUID, int eProgressState)
{
    if (m_SyncADUDataADUList.isEmpty())  //没有正在同步
    {
        return ;
    }
    if ((m_SyncADUDataADUList.contains(strADUID)) || (strADUID.toUpper() == "FF:FF:FF:FF:FF:FF:FF:FF"))
    {
//        if ((strSyncDataADUID != strADUID) && (strADUID.toUpper() != "FF:FF:FF:FF:FF:FF:FF:FF"))
//        {
//            strSyncDataADUID = strADUID;
//        }

        //同步失败信息推送
        int percentValue = eProgressState;
        if ((eProgressState == -1) || (eProgressState == 101))
        {
            QJsonObject result;
            QJsonObject data;
            data.insert("aduId",strADUID);
            result.insert(STR_SYCN_ADU_DATA_PROGRESS,data);
            for (int i = 0; i < m_clients.size(); i++)
            {
                m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
            }


            if(!m_listSyncFinish.contains(strADUID) && (strADUID.toUpper() != "FF:FF:FF:FF:FF:FF:FF:FF"))
            {
                m_listSyncFinish.append(strADUID);
            }
            percentValue = 0;
        }

        //同步进度信息推送
        QJsonObject result;
        QJsonObject data;
        const int progressIndex = m_listSyncFinish.size() < m_SyncADUDataADUList.size() ? m_listSyncFinish.size() + 1 : m_SyncADUDataADUList.size();
        data.insert("progress",QString("%1/%2").arg(progressIndex).arg(m_SyncADUDataADUList.size()));
        data.insert("percent",QString("%1").arg(percentValue));
        result.insert(STR_SYCN_ADU_DATA_PROGRESS,data);

        for (int i = 0; i < m_clients.size(); i++)
        {
            m_clients.at(i)->sendTextMessage(QJsonDocument(encapsulationData(result)).toJson());
        }


        if(eProgressState == 100)
        {
            if(!m_listSyncFinish.contains(strADUID) && (strADUID.toUpper() != "FF:FF:FF:FF:FF:FF:FF:FF"))
            {
                m_listSyncFinish.append(strADUID);
            }
        }



        //同步结束信息推送
        if (m_listSyncFinish.size() == m_SyncADUDataADUList.size() && ((eProgressState == -1) || (eProgressState == 100)))
        {
            m_listSyncFinish.clear();
            pushSyncADUDataFinsh();
        }
    }
}

/*************************************************
函数名： onLoraBeanBussy
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 相应lora 组件繁忙
*************************************************************/
void WebSocket::onLoraBeanBussy()
{
    PDS_SYS_INFO_LOG("lora busy");
    iAffairCount++;
    emit sigWorkState(true);
}

/*************************************************
函数名： onLoraBeanSpare
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 相应lora 组件空闲
*************************************************************/
void WebSocket::onLoraBeanSpare()
{
    PDS_SYS_INFO_LOG("lora spare");
    iAffairCount--;
    if (iAffairCount == 0)
    {
        emit sigWorkState(false);
    }
}
